<template>
  <view class="page">
    <!-- 顶部导航栏 -->
    <view class="header">
      <view class="header-title skeleton-item"></view>
      <view class="header-icons">
        <view class="icon-item skeleton-item"></view>
        <view class="icon-item skeleton-item"></view>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="search-bar">
      <view class="search-input skeleton-item"></view>
      <view class="search-btn skeleton-item"></view>
    </view>

    <!-- 促销横幅 -->
    <view class="banner skeleton-item"></view>

    <!-- 功能图标区域 -->
    <view class="function-icons">
      <view class="icon-group" v-for="i in 5" :key="i">
        <view class="func-icon skeleton-item"></view>
        <view class="func-text skeleton-item"></view>
      </view>
    </view>

    <!-- 店铺信息 -->
    <view class="shop-info">
      <view class="shop-header">
        <view class="shop-name skeleton-item"></view>
        <view class="shop-status skeleton-item"></view>
        <view class="shop-distance skeleton-item"></view>
        <view class="change-shop skeleton-item"></view>
      </view>
      <view class="shop-rating">
        <view class="rating-text skeleton-item"></view>
      </view>
    </view>

    <!-- 分类标签 -->
    <view class="category-tabs">
      <view class="tab-item skeleton-item" v-for="i in 6" :key="i"></view>
    </view>

    <!-- 服务项目列表 -->
    <view class="service-list">
      <view class="service-item" v-for="i in 2" :key="i">
        <view class="service-image skeleton-item"></view>
        <view class="service-content">
          <view class="service-title skeleton-item"></view>
          <view class="service-desc skeleton-item"></view>
          <view class="service-tags">
            <view class="tag skeleton-item" v-for="j in 3" :key="j"></view>
          </view>
          <view class="service-price">
            <view class="price-current skeleton-item"></view>
            <view class="price-discount skeleton-item"></view>
            <view class="price-original skeleton-item"></view>
          </view>
        </view>
        <view class="service-btn skeleton-item"></view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page {
  background: #f5f5f5;
  min-height: 100vh;
}

.skeleton-item {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  // background: linear-gradient(135deg, #ff6b6b, #ff8e8e);

  .header-title {
    width: 200rpx;
    height: 40rpx;
  }

  .header-icons {
    display: flex;
    gap: 20rpx;

    .icon-item {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
    }
  }
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  // background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  gap: 20rpx;

  .search-input {
    flex: 1;
    height: 70rpx;
    border-radius: 35rpx;
  }

  .search-btn {
    width: 70rpx;
    height: 70rpx;
    border-radius: 50%;
  }
}

.banner {
  margin: 20rpx 30rpx;
  height: 300rpx;
  border-radius: 20rpx;
}

.function-icons {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;

  .icon-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15rpx;

    .func-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
    }

    .func-text {
      width: 80rpx;
      height: 30rpx;
    }
  }
}

.shop-info {
  background: white;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 20rpx;

  .shop-header {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 20rpx;

    .shop-name {
      width: 200rpx;
      height: 40rpx;
    }

    .shop-status {
      width: 80rpx;
      height: 30rpx;
    }

    .shop-distance {
      width: 120rpx;
      height: 30rpx;
    }

    .change-shop {
      width: 100rpx;
      height: 50rpx;
      border-radius: 25rpx;
      margin-left: auto;
    }
  }

  .shop-rating {
    .rating-text {
      width: 400rpx;
      height: 30rpx;
    }
  }
}

.category-tabs {
  display: flex;
  justify-content: space-around;
  padding: 30rpx;
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;

  .tab-item {
    width: 80rpx;
    height: 30rpx;
  }
}

.service-list {
  background: white;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  padding: 30rpx;

  .service-item {
    display: flex;
    align-items: flex-start;
    gap: 20rpx;
    padding: 30rpx 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .service-image {
      width: 150rpx;
      height: 150rpx;
      border-radius: 10rpx;
      flex-shrink: 0;
    }

    .service-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 15rpx;

      .service-title {
        width: 300rpx;
        height: 35rpx;
      }

      .service-desc {
        width: 200rpx;
        height: 30rpx;
      }

      .service-tags {
        display: flex;
        gap: 10rpx;

        .tag {
          width: 60rpx;
          height: 25rpx;
        }
      }

      .service-price {
        display: flex;
        align-items: center;
        gap: 15rpx;

        .price-current {
          width: 80rpx;
          height: 35rpx;
        }

        .price-discount {
          width: 50rpx;
          height: 25rpx;
        }

        .price-original {
          width: 70rpx;
          height: 25rpx;
        }
      }
    }

    .service-btn {
      width: 120rpx;
      height: 60rpx;
      border-radius: 30rpx;
      flex-shrink: 0;
    }
  }
}
</style>
