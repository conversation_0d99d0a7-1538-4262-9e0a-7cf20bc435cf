.container {
	padding-bottom: 126rpx;
}
.storeClassContent {
	&_item {
		width: 710rpx;
		margin: 20rpx auto;
		padding: 32rpx 30rpx;
		background: #ffffff;
		&_father {
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #282828;
			&_label {
				display: flex;
				align-items: center;
				font-size: 30rpx;
				image {
					width: 30rpx;
					margin-right: 10rpx;
				}
				.iconfont{
					color: #E93323;
					font-size: 32rpx;
					margin-right: 10rpx;
				}
				
			}
			&_right {
				color: #bbbbbb;
				.iconfont{
					
					color: #282828;
				}
			}
		}

		&_child {
			margin-top: 30rpx;
			&_item {
				background: #f5f5f5;
				padding: 24rpx 30rpx;
				border-radius: 10px;
				margin-bottom: 20rpx;
				color: #282828;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				font-size: 26rpx;
			}
		}
	}

	&_tip {
		display: flex;
		align-items: center;
		color: #e93323;
		font-size: 22rpx;
		padding: 23rpx 30rpx;
		image {
			width: 23rpx;
			height: 23rpx;
			margin-right: 10rpx;
		}
	}
}

.handle_bottom {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 126rpx;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	&_button {
		width: 690rpx;
		height: 86rpx;
		background: #e93323;
		border-radius: 43rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #FFFFFF;
	}
}
