<template>
	<view :style="viewColor">
		<view class="container">
			<view class="main_content">
					<text class="title">评论 {{all}}</text>
					<image @click="close" class="closeBnt" src="../static/img/index/close.png" mode=""></image>
				<view class="main">
					<scroll-view :scroll-y="true" class="scroll-view">
						<view v-if="list.length > 0" @touchmove="onTouchmove" id="reply"> 
							<view class="common_list" v-for="(item, index) in list" :key="index">
								<view class="commen_one">
									<image :src="(item.author&&item.author.avatar) || '/static/images/f.png'" class="image"></image>
								</view>
								<view class="info_count">
									<view class="info">
										<view class="message" @click="toReply(item,index)">
											<text v-if="item.author" class="name">{{item.author.nickname}}</text>
											<text class="desc">{{item.content}}</text>
											<text class="time">{{item.create_time}}</text>
										</view>
										<view class="like" @click="starComment(item)">
											<image v-if="item.relevance_id" class="dianzan" src="../static/img/index/zan02.png"></image>
											<image v-else class="dianzan" src="../static/img/index/zan01.png"></image>
											
											<text class="like-text">{{item.count_start}}</text>
										</view>
									</view>
									<view v-if="item.children && item.children.length > 0" class="reply_count">
										<view v-for="(itemn,indexn) in item.children" :key="indexn" class="reply_list">
											<view class="item">
												<view class="item_count" @click.stop="toReply(itemn,index)">
													<image class="image" :src="itemn.author && itemn.author.avatar || '/static/images/f.png'"></image>
													<text v-if="itemn.author" class="name_two">{{itemn.author.nickname}}</text>
													<view class="desc_two">
														<text class="reply_user" v-if="itemn.reply">回复 @{{itemn.reply.nickname}} </text> 
														<text class="reply_text">{{itemn.content}}</text>
													</view>
													<text class="time_two">{{itemn.create_time}}</text>
												</view>
												<text class="like_two" @click.stop="starComment(itemn)">
													<text class="iconfont":class="itemn.relevance_id ? 'icon-icon_Like_2' : 'icon-ic_Like'"></text>
													{{itemn.count_start}}
												</text>
											</view>
										</view>								
									</view>	
								</view>	
							</view>
							<view class="end"><text class="before"></text><text class="text">到底了</text><text class="after"></text></view>
						</view>
						<Loading :loaded="loaded" :loading="loading"></Loading>
						<view v-if="list.length == 0 && !loading" class="empty">
							<image class="image" :src="`${domain}/static/images/no_commen.png`"></image>
							<text class="text">暂无评论，快去抢沙发吧~</text>
						</view>	
					</scroll-view>	
				</view>	
				<view class="release_bar" :style="'bottom:'+bottom+'px;'">
					<image class="image" :src="userInfo.avatar || '/static/images/f.png'"></image>
					<view class="input_count">
						<textarea style="height: 20px; font-size: 15px; color: #000000;" type="text" 
						:placeholder="placeholder"
						 placeholder-style="color: #999999; font-size: 15px;"
							v-model="content" :auto-focus="autoFocus" :focus="focus" confirm-type="search"></textarea>	
					</view>
					<text class="send" @click.stop="submitComment">发送</text>
				</view>
			</view>	
		</view>
	</view>	
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import { replyLstApi, starCommentApi, replyCreateApi } from '@/api/community.js';
	import Loading from '@/components/Loading/index.vue';
	import { getUserInfo } from '@/api/user.js';
	import { mapGetters } from "vuex";
	import { configMap } from '@/utils';
	import { HTTP_REQUEST_URL } from '@/config/app.js';
	import { toLogin } from '@/libs/login.js';
	export default {
		props:{
			bottom: {
				type: Number,
				default: 0
			},
			userInfo: {
				type: Object,
				default: {}
			}
		},
		components: {
		  Loading
		},
		computed: configMap({community_reply_auth:0},mapGetters(['isLogin', 'viewColor'])),
		data() {
			return {
				domain: HTTP_REQUEST_URL,
				content: '',
				id: "",
				list: [],
				loaded: false,
				loading: false,
				where: {
					page: 1,
					limit: 10
				},
				reply_id: "",
				placeholder: "快来说点儿什么吧...",
				isChild: false,
				index: 0,
				listIndex: 0,
				focus: false,
				autoFocus: false,
				all: 0,
			};
		},
		mounted() {
			let that=this;
			uni.$on('onReachBottom',function(data){  
				that.getList()
			});   
		},
		created() {
			let item = uni.getStorageSync("videoList");
			let index = uni.getStorageSync("videoIndex");
			this.getData(item,index)
		},
		// 滚动监听
		onPageScroll() {
			uni.$emit('scroll');
		},

		methods: {
			// 点击关闭按钮
			close() {
				this.$emit('closeScrollview');
			},
			onTouchmove(e){
				if (this.loadend || this.loading) return;
				const query = uni.createSelectorQuery().in(this);
				query.select('#reply').boundingClientRect(data => {
					if(data.bottom < 1500) {
						this.getList();	
					}
				}).exec();
				// 模拟触底刷新
			},
			getData(item,index){
				this.id = item.community_id
				this.loading = this.loaded = false
				this.where.page = 1
				this.list = []
				this.getList()
				this.listIndex = index
			},
			getList(){
				let that = this;
				if(that.loading || that.loaded) return;
				that.loading = true;
				replyLstApi(that.id,that.where).then(res => {
				    that.loading = false;
					that.all = res.data.all;
				    that.loaded = res.data.list.length < that.where.limit;
				    that.list.push.apply(that.list, res.data.list);
				    that.where.page = that.where.page + 1;
				  },
				  error => {
				    that.$util.Tips({
				      title: error.msg
				    })
				  }
				);
			},
			/*发表评论*/
			submitComment(){
				let that = this;
				if (that.isLogin === false) {
					toLogin()
					uni.hideKeyboard();
				}else{
					that.getUserInfo();
				}	
			},
			/**
			 * 获取个人用户信息
			 */
			getUserInfo: function() {
				let that = this;
				getUserInfo().then(res => {
					that.userInfo = res.data;
					/*判断是否绑定手机号*/
					if(res.data.phone || that.community_reply_auth == 0){
						that.createReply()
					}else{
						uni.showModal({
							title: '提示',
							 content: '请先绑定手机号',
							 success: function (res) {
								if (res.confirm) {							
								 uni.navigateTo({
									url: '/pages/users/user_phone/index?type=1'
								 })
								}else if (res.cancel) {
									uni.showToast({
										title: '已取消',
										icon: 'none'
									});
								}							 					     	    				     	 					        
							}   
						});  
					}
				});
			},
			createReply() {
				let that = this;
				let reply_id = that.reply_id ? that.reply_id : 0
				replyCreateApi(that.id,{content: that.content,reply_id: reply_id}).then(res => {
					console.log(res.message)
					if(res.status == 1){
						if(that.isChild){
							if(that.list[that.index]['children']){
								that.list[that.index]['children'].push(res.data)
							}else{
								that.list[that.index]['children'] = [res.data]
							}
						}else{
							that.list.unshift(res.data)
						}
						that.all++
					}	
					that.content = ""	
					that.$util.Tips({
						title: res.message
					});
					that.loseFocus()
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					})
				});
			},
			toReply(item,index){
				this.content = ""
				this.placeholder = '回复：'+item.author.nickname
				console.log(this.placeholder)
				this.reply_id = item.reply_id
				this.isChild = true
				this.index = index;
				this.focus = true;
				this.autoFocus = true;
			},
			loseFocus(){
				this.focus = false;
				this.autoFocus = false;
				this.reply_id = 0;
				this.placeholder = "快来说点儿什么吧..."
				this.isChild = false
				// this.content = ""
			},
			/*点赞评论*/
			starComment(item){
				let that = this;
				let status = item.relevance_id ? 0 : 1
				starCommentApi(item.reply_id,{status: status}).then(res => {
					if (res.status === 200) {
						if(item.relevance_id){
							item.count_start--;
							item.count_start = item.count_start == 0 ? 0 : item.count_start
							item.relevance_id = false
						}else{
							item.count_start++;
							item.relevance_id = true
						}
					}
					that.$util.Tips({
						title: res.message
					});
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					})
				});
			},	
		}
	}
</script>

<style lang="scss" scoped>
.main_content{
	padding: 12px 15rpx;
	border-bottom: 2px solid #F5F5F5;
	position: relative;
}
.header{
	width: 750rpx;
	position: relative;
	text-align: center;
}
.title{
	text-align: center;
	flex-direction: row;
	color: #282828;
	font-size: 36rpx;
	font-weight: bold;
}
.closeBnt{
	position: absolute;
	right: 30rpx;
	top: 30rpx;
	width: 15px;
	height: 15px;
}	
.scroll-view{
	height: 600px;
	padding-bottom: 240px;
}
.main{
	margin-top: 30px;
	padding-bottom: 10px;
	.common_list{
		position: relative;
		padding-left: 50px;
		margin-bottom: 15px;
		.commen_one{
			position: absolute;
			top: 0;
			left: 0;
			.image{
				width: 74rpx;
				height: 74rpx;
				border-radius: 100%;
			}	
		}
		.info{
			position: relative;
			padding-right: 90rpx;
			width: 630rpx;
		}
		.name,.name_two{
			color: #999999;
			font-size: 26rpx;
		}
		.desc,.desc_two{
			color: #282828;
			font-size: 28rpx;
			margin-top: 10rpx;
		}
		.desc_two{
			.reply_user{
				font-size: 24rpx;
				color: #4A8AC9;
				margin: 0 6rpx;
			}
			.reply_text{
				font-size: 26rpx;
				color: #282828;
			}
		}
		.time,.time_two{
			color: #BBBBBB;
			font-size: 22rpx;
			margin-top: 15rpx;
		}
		.like,.like_two{
			text-align: center;
			position: absolute;
			top: 0;
			right: 0;
			width: 75rpx;
			.like-text{
				color: #999999;
				font-size: 13px;
				margin-top: 2px;
			}
			.dianzan{
				width: 13px;
				height: 13px;
			}
		}
		.reply_list{
			margin-top: 20rpx;
			.item{
				padding-right: 140rpx;
				position: relative;
			}
			.item_count{
				position: relative;
				padding-left: 56rpx;
				.image{
					width: 36rpx;
					height: 36rpx;
					border-radius: 100%;
					position: absolute;
					top: 0;
					left: 0;
				}
			}
		}
	}
	.end{
		margin: 50rpx 0;
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
		.text{
			color: #999999;
			font-size: 22rpx;
			position: relative;
			.before,.after{
				display: flex;
				width: 22rpx;
				height: 1rpx;
				background: #999999;
				position: absolute;
				top: 18rpx;
				opacity: .5;
			}
			.before{
				left: -30rpx;
			}
			.after{
				right: -30rpx;
			}
		}
	}
}
.release_bar{
	position: fixed;
	width: 100%;
	left: 0;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 16rpx 30rpx;
	border-top: 1rpx solid #F5F5F5;
	flex-direction: row;
	width:750rpx;
	.image{
		width: 54rpx;
		height: 54rpx;
		border-radius: 100%;
	}
	.input_count{
		width: 480rpx;
		background: #F7F7F7;
		border-radius: 31rpx;
		padding: 12rpx 30rpx;
	}
	.send{
		font-size: 26rpx;
		color: #ffffff;
		padding: 12rpx 30rpx;
		background: #E93323;
		border-radius: 30rpx;
		text-align: center;
	}
}
.empty{
	display: flex;
	margin: 130rpx 0 150rpx;
	text-align: center;
	align-items: center;
	// flex-direction: row;
	.image{
		display: flex;
		width: 414rpx;
		height: 305rpx;
	}
	.text{
		display: flex;
		color: #999999;
		font-size: 26rpx;
	}
}
</style>
