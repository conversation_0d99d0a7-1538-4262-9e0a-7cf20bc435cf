// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import store from '@/store'

/**
 * 门店信息管理工具类
 */
export default {
  /**
   * 设置门店信息到 Vuex 和本地存储
   * @param {Object} storeInfo 门店信息对象
   */
  setStoreInfo(storeInfo) {
    store.commit('SET_STORE_INFO', storeInfo)
  },

  /**
   * 获取门店信息
   * @returns {Object} 门店信息对象
   */
  getStoreInfo() {
    return store.getters.storeInfo
  },

  /**
   * 清除门店信息
   */
  clearStoreInfo() {
    store.commit('CLEAR_STORE_INFO')
  },

  /**
   * 检查是否有门店信息
   * @returns {Boolean} 是否有门店信息
   */
  hasStoreInfo() {
    const storeInfo = this.getStoreInfo()
    return storeInfo && Object.keys(storeInfo).length > 0 && storeInfo.mer_id
  },

  /**
   * 获取门店ID
   * @returns {String|Number} 门店ID
   */
  getStoreId() {
    const storeInfo = this.getStoreInfo()
    return storeInfo.mer_id || ''
  },

  /**
   * 获取门店名称
   * @returns {String} 门店名称
   */
  getStoreName() {
    const storeInfo = this.getStoreInfo()
    return storeInfo.mer_name || ''
  }
}
