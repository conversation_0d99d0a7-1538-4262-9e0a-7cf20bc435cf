<template>
  <view class="exchange-record-page">
    <!-- 筛选标签 -->
    <view class="tabs">
      <view
        v-for="(tab, idx) in tabs"
        :key="tab.key"
        :class="['tab', { active: selectedTab === tab.key }]"
        @tap="selectTab(tab.key)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <view v-for="order in filteredOrders" :key="order.id" class="order-card">
        <view class="order-header">
          <view class="shop-title center">
            {{ order.shop }}
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
          <text class="order-status">{{ order.statusLabel }}</text>
        </view>
        <view class="order-body">
          <image class="product-img" :src="order.img" mode="aspectFill" />
          <view class="product-info">
            <view class="product-title">{{ order.title }}</view>
            <view class="product-desc">{{ order.desc }}</view>
            <view class="order-time">下单时间：{{ order.time }}</view>
          </view>
        </view>
        <!-- 退款信息 -->
        <view v-if="order.status === 'refund_success'" class="refund-info">
          <text class="refund-info-lable">退款成功：</text>
          <text class="refund-amount">退回至余额钱包</text>
          <text class="refund-price">¥{{ order.refundAmount }}</text>
        </view>
        <view class="order-footer">
          <view class="order-total">
            <view class="order-total__title">合计：</view>
            <text class="price">{{ order.price }}+{{ order.privilege }}御享值</text>
          </view>
          <view class="order-actions">
            <template v-if="order.status === 'pending_pay'">
              <button class="btn default" @tap="cancelOrder(order.id)">取消</button>
              <button class="btn primary" @tap="continuePay(order.id)">继续付款</button>
            </template>
            <template v-else-if="order.status === 'pending_use'">
              <button class="btn default" @tap="applyRefund(order.id)">申请售后</button>
              <button class="btn primary" @tap="viewCode(order.id)">查看券码</button>
            </template>
            <template v-else-if="order.status === 'finished'">
              <button class="btn primary" @tap="reorder(order.id)">再来一单</button>
            </template>
            <template v-else-if="order.status === 'refund_success'">
              <!-- 无操作按钮 -->
            </template>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabs: [
        { key: 'all', label: '全部' },
        { key: 'pending_pay', label: '待支付' },
        { key: 'pending_use', label: '待使用' },
        { key: 'finished', label: '已完成' },
        { key: 'refund', label: '退款/售后' },
      ],
      selectedTab: 'all',
      orders: [
        {
          id: 1,
          shop: '御理清享（全部门店）',
          title: '施华蔻洗发水',
          desc: '500ml*1瓶',
          img: 'https://dummyimage.com/340x340/eeeeee/aaa&text=商品1', // 替换为本地图片路径
          time: '2025-05-08',
          price: 88.6,
          privilege: 60,
          status: 'pending_pay', // 待支付
          statusLabel: '待支付',
        },
        {
          id: 2,
          shop: '御理清享（全部门店）',
          title: '施华蔻洗发水',
          desc: '500ml*1瓶',
          img: 'https://dummyimage.com/340x340/eeeeee/aaa&text=商品1',
          time: '2025-05-08',
          price: 88.6,
          privilege: 60,
          status: 'pending_use', // 待使用
          statusLabel: '待使用',
        },
        {
          id: 3,
          shop: '御理清享（全部门店）',
          title: '施华蔻洗发水',
          desc: '500ml*1瓶',
          img: 'https://dummyimage.com/340x340/eeeeee/aaa&text=商品1',
          time: '2025-05-08',
          price: 88.6,
          privilege: 60,
          status: 'finished', // 已完成
          statusLabel: '已完成',
        },
        {
          id: 4,
          shop: '御理清享（全部门店）',
          title: '施华蔻洗发水',
          desc: '500ml*1瓶',
          img: 'https://dummyimage.com/340x340/eeeeee/aaa&text=商品1',
          time: '2025-05-08',
          price: 88.6,
          privilege: 60,
          status: 'refund_success', // 退款成功
          refundAmount: 88.6,
          statusLabel: '退款成功',
        },
      ],
    }
  },
  computed: {
    filteredOrders() {
      if (this.selectedTab === 'all') {
        return this.orders
      } else if (this.selectedTab === 'refund') {
        return this.orders.filter((o) => o.status === 'refund_success')
      }
      return this.orders.filter((o) => o.status === this.selectedTab)
    },
  },
  methods: {
    selectTab(key) {
      this.selectedTab = key
    },
    goBack() {
      uni.navigateBack()
    },
    cancelOrder(id) {
      // 取消订单逻辑
    },
    continuePay(id) {
      // 跳转支付
    },
    applyRefund(id) {
      // 跳转售后
    },
    viewCode(id) {
      // 查看券码
    },
    reorder(id) {
      // 再来一单
    },
  },
}
</script>

<style scoped lang="scss">
.exchange-record-page {
  background: #fafafa;
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.tabs {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  padding: 20rpx;

  .tab {
    text-align: center;
    font-size: 28rpx;
    color: #888;
    position: relative;
    padding: 8rpx 16rpx;
  }
  .active {
    color: white;
    font-weight: bold;
    background: #c9a063;
    border-radius: 36rpx;
  }
}

.order-list {
  padding: 40rpx 30rpx;
  .order-card {
    background: #fff;
    border-radius: 28rpx;
    margin-bottom: 28rpx;
    padding: 28rpx 24rpx 20rpx 24rpx;
    box-shadow: 0 2rpx 12rpx #f4f4f4;
  }
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 28rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 16rpx;
  }

  .shop-title {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
  }
  .order-status {
    font-size: 26rpx;
    font-weight: 400;
  }
  .order-body {
    display: flex;
    align-items: flex-start;
    margin-bottom: 14rpx;
    margin-top: 40rpx;
  }
  .product-img {
    width: 98rpx;
    height: 98rpx;
    border-radius: 12rpx;
    margin-right: 20rpx;
  }
  .product-info {
    flex: 1;
  }
  .product-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
  }
  .product-desc {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 10rpx;
  }
  .order-time {
    font-size: 24rpx;
    font-weight: 300;
    color: #999;
  }
  .refund-info {
    background: #f5f3ef;
    display: flex;
    align-items: center;
    font-size: 24rpx;
    border-radius: 8rpx;
    padding: 16rpx 18rpx;
    margin-bottom: 20rpx;
    .refund-info-lable {
      color: #333333;
      font-weight: 500;
    }
    .refund-price {
      color: #c9a063;
      margin-left: 8rpx;
    }
    .refund-amount {
      color: #999;
      margin-left: 6rpx;
    }
  }

  .order-footer {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    // justify-content: flex-end;
    // margin-top: 10rpx;
  }
  .order-total {
    font-size: 28rpx;
    display: flex;
    align-items: center;
    margin-bottom: 26rpx;
    color: #222;
    &__title {
      color: #999999;
    }
  }
  .price {
    color: #333333;
    font-weight: 500;
    &::before {
      content: '￥';
      font-size: 26rpx;
    }
  }
  .order-actions {
    display: flex;
    align-items: center;
  }
  .btn {
    font-size: 26rpx;
    border-radius: 36rpx;
    padding: 8rpx 32rpx;
    margin-left: 18rpx;
    border: 1rpx solid #c9a063;
    background: #fff;
    color: #c9a063;
  }
  .btn.primary {
    background: #c9a063;
    color: #fff;
    border: none;
  }
  .btn.default {
    background: #fff;
    color: #999999;
    border: 1rpx solid #999999;
  }
}
</style>
