<template>
  <view class="coupon-center">
    <!-- 优惠券列表 -->
    <view class="coupon-center__list">
      <!-- 第一张券 -->
      <view class="coupon-center__item">
        <view class="coupon-center__main">
          <view class="coupon-center__price-block">
            <view class="coupon-center__price">¥80</view>
            <view class="coupon-center__desc">满300减80</view>
          </view>
          <view class="coupon-center__body">
            <view class="coupon-center__title">烫染直护</view>
            <view class="coupon-center__limit">限挚友会员</view>
          </view>
          <button class="coupon-center__btn" hover-class="btnHoverClass">领取</button>
        </view>
        <view class="coupon-center__footer">
          <view class="coupon-center__rule">
            使用规则
            <text class="iconfont icon-ic_downarrow"></text>
          </view>
          <view class="coupon-center__expire">有效期至 2026.05.07</view>
        </view>
      </view>

      <!-- 第二张券 展开 -->
      <view class="coupon-center__item coupon-center__item--active">
        <view class="coupon-center__main">
          <view class="coupon-center__price-block">
            <view class="coupon-center__price">¥80</view>
            <view class="coupon-center__desc">满300减80</view>
          </view>
          <view class="coupon-center__body">
            <view class="coupon-center__title">烫染直护</view>
            <view class="coupon-center__limit">限挚友会员</view>
          </view>
          <button class="coupon-center__btn" hover-class="btnHoverClass">领取</button>
        </view>
        <view class="coupon-center__footer coupon-center__footer--active">
          <view class="coupon-center__rule">使用规则</view>
          <view class="coupon-center__expire">有效期至 2026.05.07</view>
        </view>
        <!-- 展开规则 -->
        <view class="coupon-center__detail">
          <view class="coupon-center__detail-border">
            <view>
              <text class="label">优惠金额：</text>
              <text>¥80</text>
            </view>
            <view>
              <text class="label">使用范围：</text>
              <text>所有门店通用</text>
            </view>
            <view>
              <text class="label">不可用服务：</text>
              <text>洗剪吹</text>
            </view>
            <view>
              <text class="label">权益叠加：</text>
              <text>不可与其他优惠叠加使用</text>
            </view>
            <view>
              <text class="label">可用发型师：</text>
              <text>高级、金牌</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 第三张券 -->
      <view class="coupon-center__item">
        <view class="coupon-center__main">
          <view class="coupon-center__price-block">
            <view class="coupon-center__price">¥38.8</view>
            <view class="coupon-center__desc">无门槛</view>
          </view>
          <view class="coupon-center__body">
            <view class="coupon-center__title">免费洗吹代金券</view>
            <view class="coupon-center__limit">限挚友会员</view>
          </view>
          <button class="coupon-center__btn" hover-class="btnHoverClass">领取</button>
        </view>
        <view class="coupon-center__footer">
          <view class="coupon-center__rule">使用规则</view>
          <view class="coupon-center__expire">有效期至 2026.05.07</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default { name: 'CouponCenter' }
</script>

<style lang="scss" scoped>
.coupon-center {
  background: #fff;
  min-height: 100vh;

  &__list {
    padding: 40rpx 30rpx;
  }
  &__item {
    background: #ffefd4;
    border-radius: 30rpx;
    margin-bottom: 20rpx;
    position: relative;
  }
  &__main {
    display: flex;
    align-items: center;
    width: 100%;
    background: linear-gradient(0deg, #fffaf2 0%, #fff0d7 100%);
    border-radius: 30rpx;
    position: relative;
    // padding: 40rpx 0;
    &::after,
    &::before {
      content: ' ';
      display: block;
      width: 28rpx;
      height: 28rpx;
      border-radius: 50%;
      position: absolute;
    }
    &::after {
      top: -14rpx;
      left: 176rpx;
      background: #ffffff;
    }
    &::before {
      bottom: -14rpx;
      left: 176rpx;
      background: #fff0d7;
    }
  }
  &__price-block {
    border-right: 2rpx dashed white;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx;
  }
  &__price {
    color: #b6341c;
    font-size: 48rpx;
    font-weight: 700;
  }
  &__desc {
    color: #b6341c;
    font-size: 22rpx;
    margin-top: 4rpx;
  }
  &__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 12rpx;
    padding: 40rpx;
  }
  &__title {
    font-size: 28rpx;
    color: #222;
    font-weight: 600;
    margin-bottom: 4rpx;
  }
  &__limit {
    font-size: 22rpx;
    color: #e2b47b;
  }
  &__btn {
    background: #c9a063;
    border-radius: 12rpx;
    font-size: 26rpx;
    color: #ffffff;
    padding: 12rpx 38rpx;
    margin-right: 20rpx;
  }
  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 40rpx;
  }
  &__rule {
    font-weight: 300;
    font-size: 22rpx;
    color: #666666;
    .iconfont {
      font-size: 24rpx;
      color: #b9b9b9;
      margin-left: 10rpx;
    }
  }
  &__expire {
    font-weight: 300;
    font-size: 22rpx;
    color: #666666;
  }
  &__detail {
    padding: 0 28rpx 40rpx;
    &-border {
      border-top: 1rpx dashed #999999;
      padding-top: 20rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      .label {
        color: #000000;
      }
    }
  }
}
</style>
