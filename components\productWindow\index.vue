<template>
	<view :style="viewColor">
		<view class="product-window" :class="(attr.cartAttr === true ? 'on' : '') + ' ' + (iSbnt || destri || (isList&&!isTab)?'join':'') + ' ' + (isPresell ?'presell-window':'') + ' ' + (isCustom ?'custom-window':'')">
			<view class="textpic acea-row row-between-wrapper">
				<view class="pictrue">
					<image :src="attr.productSelect.image ? attr.productSelect.image : image" @click="loookImg"></image>
				</view>
				<view class="text">
					<view class="line1">
						{{ attr.productSelect.store_name }}
					</view>
					<view v-if="isPresell" class="money presell_price">
						<view>
							<text style="margin-right: 2rpx;">预售价</text> <priceFormat :price="attr.productSelect.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
							<text v-if="presell_type === 2" style="margin-left:10rpx;">定金¥<text class="num semiBold">{{ attr.productSelect.down_price }}</text></text>
						</view>
						<text class="stock" v-if='isShow'>库存: {{ attr.productSelect.stock }}</text>
						<text class='stock' v-if="limitNum">限量: {{attr.productSelect.limitNum}}</text>
					</view>
					<view v-else class="money">
						<view class="acea-row row-middle">
							<view class="acea-row row-bottom">
								<view v-if="type=='points'" class="points_money">
									<image :src="`${domain}/static/images/jf-point.png`" mode=""></image>
									<text class="points-num">{{attr.productSelect.ot_price}}</text>积分 <text v-if="attr.productSelect.price > 0">+{{attr.productSelect.price}}元</text>
									<text v-if="limitCount > 0" class="points-limit">限购{{limitCount}}件</text>
								</view>
								<view v-else>
									<priceFormat :price="attr.productSelect.price" weight intSize="40" floatSize="26" labelSize="26"></priceFormat>
								</view>
								<view v-if="svipPrice" class="acea-row row-middle">
									<text class='vip-money semiBold'>¥{{attr.productSelect.svip_price}}</text>
									<view class="vipImg">
										<image :src="`${domain}/static/images/svip.png`"></image>
									</view>
								</view>
							</view>
						</view>
						<view class="stock_count">
							<text class="stock" v-if='isShow'>库存: {{ attr.productSelect.stock }}</text>
							<text class='stock' v-if="limitNum">限量: {{attr.productSelect.quota_show}}</text>
						</view>
					</view>
				</view>
				<view class="iconfont icon-ic_close popup-close" @click="closeAttr"></view>
			</view>
			<view class="productWinList">
				<view class="item" v-for="(item, indexw) in attr.productAttr" :key="indexw">
					<view class="titles">{{ item.attr_name }}</view>
					<view class="listn acea-row">
						<view class="itemn" :class="item.index === itemn.attr ? 'on' : ''" v-for="(itemn, indexn) in item.attr_value"
						 @click="tapAttr(indexw, indexn)" :key="indexn">
							{{ itemn.attr }}
						</view>
					</view>
				</view>
			</view>
			<view v-if="productType != 3" class="cart acea-row row-between-wrapper">
				<view class="title">数量</view>
				<view class="carnum acea-row row-left">
					<view class="buy_limit" v-if="minCount>0 || maxCount>0">
						(<text v-if="minCount>0">{{minCount}}件起购<text v-if="minCount>0 && maxCount>0">，</text></text><text v-if="maxCount>0">最多{{maxCount}}件</text>)
					</view>
					<view class="item reduce" :class="(attr.productSelect.cart_num <= 1 || (minCount>0 && attr.productSelect.cart_num<=minCount)) ? 'on' : ''" @click="CartNumDes">
						<text class="iconfont icon-ic_Reduce"></text>
					</view>
					<view class='item num'>
						<input type="number" v-model="attr.productSelect.cart_num" data-name="productSelect.cart_num" @input="bindCode(attr.productSelect.cart_num)"></input>
					</view>
					<view v-if="iSplus" class="item plus" :class="(attr.productSelect.cart_num >= attr.productSelect.stock || destri || (maxCount>0&&attr.productSelect.cart_num>=maxCount)) ? 'on' : ''"
					 @click="CartNumAdd"><text class="iconfont icon-ic_increase"></text></view>
					<view v-else class='item plus' :class='((attr.productSelect.cart_num >= attr.productSelect.quota_show) 
						|| (attr.productSelect.cart_num >= attr.productSelect.stock) || (maxCount>0&&attr.productSelect.cart_num>=maxCount)) ? "on":""'
					 @click='CartNumAdd'><text class="iconfont icon-ic_increase"></text></view>
				</view>
			</view>
			<view class="joinBnt b-color" v-if="(destri || iSbnt) && attr.productSelect.stock>0" @click="goCat">立即购买</view>
			<view class="joinBnt on" v-else-if="destri && attr.productSelect.stock<=0">已售罄</view>
			<view class="joinBnt b-color" v-else-if="isList && attr.productSelect.stock>0" @click="goCat">立即加购</view>
			<view class="joinBnt on" v-else-if="(iSbnt && attr.productSelect.stock<=0)">已售罄</view>
		</view>
		<view class="mask" @touchmove.prevent :hidden="attr.cartAttr === false" @click="closeAttr"></view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import { mapGetters } from "vuex";
	import { HTTP_REQUEST_URL } from '@/config/app';
	import { toLogin } from '@/libs/login.js';
	export default {	
		props: {
			attr: {
				type: Object,
				default: () => {}
			},
			limitNum: {
				type: Number,
				value: 0
			},
			limitCount: {
				type: Number || String,
				value: 0
			},
			isShow: {
				type: Number,
				value: 0
			},
			iSbnt: { //参团
				type: Number,
				value: 0
			},
			iSplus: {
				type: Number,
				value: 0
			},
			destri: {
				type: Number,
				value: 0
			},
			isPresell: {
				type: Number,
				value: 0
			},
			presell_type: {
				type: Number,
				value: 1
			},
			image: {
				type: String,
				value: ''
			},
			maxCount: {
				type: Number,
				value: 0
			},
			minCount: {
				type: Number,
				value: 0
			},
			payLimit: {
				type: Number || String,
				value: 1
			},
			svipPrice: {
				type: Boolean,
				value: false
			},
			isList: {
				type: Boolean,
				value: false
			},
			isTab: {
				type: Boolean,
				value: false
			},
			type: {
				type: String,
				value: ''
			},
			productType: {
				type: Number || String,
				value: 1
			},
			isCustom: {
				type: Boolean,
				default: () => false
			}
		},
		computed: mapGetters(['viewColor', 'isLogin']),
		data() {
			return {
				domain: HTTP_REQUEST_URL
			};
		},

		mounted(){},
		methods: {
			//  查看大图
			loookImg(){
				let self = this
				let arr = [self.attr.productSelect.image ? self.attr.productSelect.image : self.image]
				uni.previewImage({
					urls: arr,
				});
			},
			goCat: function() {
				if(!this.isLogin)toLogin()
				this.$emit('goCat');
			},
			/**
			 * 购物车手动输入数量
			 * 
			 */
			bindCode: function(e) {
				let num = JSON.parse(JSON.stringify(e))
				this.$emit('iptCartNum', num);
			},
			closeAttr: function() {
				this.$emit('myevent');
			},
			CartNumDes: function() {
				if (!this.destri) {
					this.$emit('ChangeCartNum', false);
				}
			},
			CartNumAdd: function() {
				if (!this.destri) {
					this.$emit('ChangeCartNum', true);
				}
			},
			tapAttr: function(indexw, indexn) {
				let that = this;
				that.$emit("attrVal", {
					indexw: indexw,
					indexn: indexn
				});
				this.$set(this.attr.productAttr[indexw], 'index', this.attr.productAttr[indexw].attr_values[indexn]);
				let value = that.getCheckedValue().join(",");
				that.$emit("ChangeAttr", value);
			},
			//获取被选中属性；
			getCheckedValue: function() {
				let productAttr = this.attr.productAttr;
				let value = [];
				for (let i = 0; i < productAttr.length; i++) {
					for (let j = 0; j < productAttr[i].attr_values.length; j++) {
						if (productAttr[i].index === productAttr[i].attr_values[j]) {
							value.push(productAttr[i].attr_values[j]);
						}
					}
				}
				return value;
			},
		 
		}
	}
</script>

<style scoped lang="scss">
	.product-window {
		position: fixed;
		bottom: 0;
		width: 100%;
		left: 0;
		background-color: #fff;
		z-index: 77;
		border-radius: 40rpx 40rpx 0 0;
		/*#ifdef H5*/
		padding-bottom: 150rpx;
		padding-bottom: calc(150rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
		padding-bottom: calc(150rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
		/*#endif*/
		/*#ifdef MP || APP-PLUS*/
		padding-bottom: 44rpx;
		/*#endif*/
		transform: translate3d(0, 100%, 0);
		transition: all .3s cubic-bezier(.25, .5, .5, .9);	
		&.presell-window {
			/*#ifdef H5 || APP-PLUS*/
			padding-bottom: 200rpx;
			/*#endif*/
			/*#ifdef MP*/
			padding-bottom: 260rpx;
			/*#endif*/
		}
		&.custom-window {
			padding-bottom: 120rpx;
			padding-bottom: calc(120rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
			padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
		}
	}
	.product-window.on {
		transform: translate3d(0, 0, 0);
	}
	.product-window.join {
		padding-bottom: 30rpx;
	}
	.product-window .textpic {
		padding: 0 80rpx 0 30rpx;
		margin-top: 30rpx;
		position: relative;
	}
	.product-window .textpic .pictrue {
		width: 150rpx;
		height: 150rpx;
	}
	.product-window .textpic .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}
	.product-window .textpic .text {
		width: 460rpx;
		font-size: 32rpx;
		color: #282828;
	}
	.product-window .textpic .text .money .points_money {
		display: flex;
		align-items: baseline;
	}
	.product-window .textpic .text .money {
		font-size: 22rpx;
		margin-top: 16rpx;
		color: var(--view-priceColor);
	}
	.product-window .textpic .text .money .points_money image{
		width: 26rpx;
		height: 26rpx;
		margin-right: 10rpx;
	}
	.product-window .textpic .text .money .points_money .points-num{
		font-size: 34rpx;
		font-weight: bold;
	}
	.product-window .textpic .text .money .points_money .points-limit{
		display: flex;
		padding: 0 10rpx;
		align-items: center;
		justify-content: center;
		color: var(--view-theme);
		border: 1px solid var(--view-theme);
		height: 26rpx;
		border-radius: 14rpx;
		background: var(--view-bgColor);
		margin-left: 10rpx;
		font-size: 18rpx;
	}
	.product-window .stock_count{
		margin-top: 12rpx;
	}
	.product-window .textpic .text .money .num {
		font-size: 24rpx;
	}
	.product-window .textpic .text .money .stock {
		color: #999;
		margin-top: 10rpx;
		font-weight: normal;
	}
	.product-window .textpic .text .presell_price {
		color: #FF7F00;
		.num {
			display: inline-block;
			margin-right: 20rpx;
		}
		.stock {
			margin-left: 0;
		}
	}
	.vip-money {
		color: #282828;
		font-size: 22rpx;
		margin: 0 10rpx 0 20rpx;
	}
	.vipImg {
		width: 65rpx;
		height: 28rpx;
		image {
			width: 100%;
			height: 100%;
			display: block;
		}
	}
	.product-window .textpic .iconfont {
		position: absolute;
		right: 20rpx;
		top: -10rpx;
	}
	.product-window .productWinList {
		max-height: 730rpx;
		overflow: auto;
		margin-top: 36rpx;
	}
	.product-window .cart {
		margin-top: 36rpx;
		padding: 0 30rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}
	.product-window .cart .title {
		font-size: 30rpx;
		color: #999;
		line-height: 54rpx;
	}
	.product-window .cart .carnum {
		align-items: center;
	}
	.product-window .cart .carnum .buy_limit {
		font-size: 22rpx;
		color: var(--view-theme);
		margin-right: 30rpx;
		border: none;
	}
	.product-window .joinBnt {
		font-size: 30rpx;
		width: 690rpx;
		height: 86rpx;
		border-radius: 50rpx;
		text-align: center;
		line-height: 86rpx;
		color: #fff;
		margin: 21rpx auto 0 auto;
		&.b-color{
			background-color: var(--view-theme);
		}
	}
	.product-window .joinBnt.on {
		background-color: #bbb;
		color: #fff;
		background-image: none;
	}
</style>
