<template>
  <view class="works-management">
    <!-- 作品列表 -->
    <view class="works-list">
      <view v-if="loading && worksList.length === 0" class="loading-container">
        <my-skeleton></my-skeleton>
      </view>
      <view v-else-if="worksList.length === 0" class="empty-container">
        <view class="empty-text">暂无作品</view>
        <view class="empty-desc">快来发布你的第一个作品吧</view>
      </view>
      <view v-else class="works-grid">
        <view
          class="work-card"
          v-for="item in worksList"
          :key="item.id"
          @click="toWorkDetails(item)"
        >
          <view class="card-image">
            <image :src="getImageUrl(item.files)" mode="aspectFill"></image>
            <!-- 标签覆盖层 -->
            <view class="image-overlay">
              <view class="work-tags" v-if="item.tags && item.tags.length">
                <text class="tag" v-for="(tag, index) in item.tags.slice(0, 2)" :key="tag">
                  {{ tag }}
                </text>
              </view>
            </view>
            <!-- 操作按钮 -->
            <view class="card-actions">
              <!-- <view class="action-btn edit-btn" @click.stop="editWork(item)">
                <text class="iconfont icon-ic_edit"></text>
              </view> -->
              <!-- <view class="action-btn delete-btn" @click.stop="deleteWorkItem(item)">
                <text class="iconfont icon-ic_delete"></text>
              </view> -->
            </view>
            <!-- 播放按钮（如果是视频） -->
            <view class="play-btn" v-if="isVideo(item.files)">
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view>
          <view class="card-content">
            <view class="work-title">{{ item.title }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 加载更多 -->
    <view v-if="worksList.length > 0" class="load-more">
      <view v-if="loading" class="loading-text">加载中...</view>
      <view v-else-if="hasMore" class="load-more-btn" @click="loadMore">加载更多</view>
      <view v-else class="no-more-text">没有更多了</view>
    </view>
    <!-- 顶部操作栏 -->
    <view class="footer-bar ios-pb">
      <button hover-class="button-hover" class="add-btn" @click="showPublishModal">
        <text class="iconfont icon-ic_increase"></text>
        发布作品
      </button>
    </view>
  </view>
</template>

<script>
import { getWorksStoreIdList, deleteWork } from '@/api/hairdressing.js'

export default {
  components: {
    'my-skeleton': () => import('@/components/my-skeleton/index.vue'),
  },
  data() {
    return {
      merId: 6, // 商家ID，实际应该从路由参数或用户信息获取
      worksList: [],
      loading: false,
      hasMore: true,
      page: 1,
      limit: 10,
    }
  },

  onLoad(options) {
    console.log('🚀 ~ onLoad ~ options:', options)
    if (options && options.mer_id) {
      this.merId = parseInt(options.mer_id)
      // this.loadWorksList()
    }
  },
  onShow() {
    if (this.merId) {
      this.refreshList()
    }
  },

  onReachBottom() {
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
  },

  onPullDownRefresh() {
    this.refreshList()
  },

  methods: {
    // 加载作品列表
    async loadWorksList(isRefresh = false) {
      if (this.loading) return
      this.loading = true
      if (isRefresh) {
        this.page = 1
        this.hasMore = true
      }

      try {
        const res = await getWorksStoreIdList(this.merId, {
          page: this.page,
          limit: this.limit,
        })

        if (res.status === 200) {
          const newList = res.data.list || []
          console.log('🚀 ~ loadWorksList ~ newList:', newList)
          newList.forEach((item) => {
            item.files = item.files.map((file) => {
              //判断图片是否 含有 'http' 或 'https'，没有的话就加上域名
              if (!file.startsWith('http://') && !file.startsWith('https://')) {
                return `https://ylqx-file.oss-cn-chengdu.aliyuncs.com/${file}` // 替换为实际的域名
              }
              return file
            })
          })
          if (isRefresh) {
            this.worksList = newList
          } else {
            this.worksList = [...this.worksList, ...newList]
          }

          this.hasMore = newList.length === this.limit
          if (this.hasMore) {
            this.page++
          }
        }
      } catch (error) {
        uni.showToast({
          title: '加载失败',
          icon: 'none',
        })
      } finally {
        this.loading = false
        if (isRefresh) {
          uni.stopPullDownRefresh()
        }
      }
    },

    // 加载更多
    loadMore() {
      this.loadWorksList()
    },

    // 刷新列表
    refreshList() {
      this.loadWorksList(true)
    },

    // 跳转到发布页面
    showPublishModal() {
      uni.navigateTo({
        url: `./publish?merId=${this.merId}`,
      })
    },

    // 删除作品
    deleteWorkItem(item) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个作品吗？',
        success: async (res) => {
          if (res.confirm) {
            try {
              uni.showLoading({ title: '删除中...' })

              const result = await deleteWork(this.merId, item.id)

              if (result.status === 200) {
                uni.showToast({
                  title: '删除成功',
                  icon: 'success',
                })
                this.refreshList()
              }
            } catch (error) {
              console.error('删除作品失败:', error)
              uni.showToast({
                title: '删除失败',
                icon: 'none',
              })
            } finally {
              uni.hideLoading()
            }
          }
        },
      })
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''

      try {
        const date = new Date(time)
        const now = new Date()
        const diff = now - date

        if (diff < 60000) {
          return '刚刚'
        } else if (diff < 3600000) {
          return Math.floor(diff / 60000) + '分钟前'
        } else if (diff < 86400000) {
          return Math.floor(diff / 3600000) + '小时前'
        } else if (diff < 604800000) {
          return Math.floor(diff / 86400000) + '天前'
        } else {
          return date.getMonth() + 1 + '-' + date.getDate()
        }
      } catch (error) {
        console.error('时间格式化失败:', error)
        return time
      }
    },

    // 图片加载失败处理
    handleImageError(e) {
      console.log('图片加载失败:', e)
    },

    // 获取图片URL
    getImageUrl(files) {
      if (!files || !Array.isArray(files) || files.length === 0) {
        return '/static/images/default-image.png' // 默认图片
      }
      return files[0]
    },
    previewImage(index, item) {
      uni.previewImage({
        current: index,
        urls: item.files,
      })
    },
    // 获取头像URL
    getAvatarUrl(storeService) {
      if (!storeService || !storeService.avatar) {
        return '/static/images/default-avatar.png' // 默认头像
      }
      return storeService.avatar
    },

    // 获取作者名称
    getAuthorName(storeService) {
      if (!storeService || !storeService.nickname) {
        return '未知用户'
      }
      return storeService.nickname
    },

    // 判断是否为视频
    isVideo(files) {
      if (!files || !Array.isArray(files) || files.length === 0) {
        return false
      }
      const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']
      const fileUrl = files[0].toLowerCase()
      return videoExtensions.some((ext) => fileUrl.includes(ext))
    },
    // 跳转到作品详情
    toWorkDetails(item) {
      uni.navigateTo({
        url: `../work_details/index?info=${encodeURIComponent(JSON.stringify(item))}&merId=${
          this.merId
        }`,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.works-management {
  background: #f8f8f8;
  min-height: 100vh;
}

.works-list {
  padding: 20rpx;
}

.works-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.loading-container {
  padding: 20rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 28rpx;
    color: #999;
  }
}

.work-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }

  .card-image {
    position: relative;
    width: 100%;
    height: 400rpx;
    overflow: hidden;

    image {
      width: 100%;
      height: 100%;
    }

    .image-overlay {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
      padding: 40rpx 20rpx 20rpx;

      .work-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8rpx;

        .tag {
          padding: 6rpx 12rpx;
          background: #fff3e1;
          color: #d19e58;
          font-size: 22rpx;
          border-radius: 12rpx;
          backdrop-filter: blur(10rpx);
        }
      }
    }

    .card-actions {
      position: absolute;
      top: 12rpx;
      left: 12rpx;
      display: flex;
      gap: 8rpx;
      transition: all 0.3s ease;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        border-radius: 50%;
        backdrop-filter: blur(10rpx);

        .iconfont {
          font-size: 28rpx;
        }

        &.edit-btn {
          background: rgba(14, 165, 233, 0.9);
          color: white;
        }

        &.delete-btn {
          background: rgba(239, 68, 68, 0.9);
          color: white;
        }
      }
    }

    .play-btn {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      color: white;
      backdrop-filter: blur(10rpx);

      .iconfont {
        font-size: 32rpx;
        margin-left: 4rpx;
      }
    }
  }

  .card-content {
    padding: 24rpx;

    .work-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 16rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .author-info {
      display: flex;
      align-items: center;
      gap: 12rpx;

      .avatar {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        flex-shrink: 0;
      }

      .author-details {
        flex: 1;

        .author-name {
          display: block;
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
        }

        .author-role {
          display: block;
          font-size: 22rpx;
          color: #999;
          margin-top: 2rpx;
        }
      }
    }
  }
}

.work-item {
  display: flex;
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }

  .work-image {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 16rpx;
    overflow: hidden;
    flex-shrink: 0;
    margin-right: 24rpx;
    background: #f5f5f5;

    image {
      width: 100%;
      height: 100%;
      transition: all 0.3s ease;
    }

    .status-badge {
      position: absolute;
      top: 8rpx;
      right: 8rpx;
      padding: 4rpx 12rpx;
      border-radius: 20rpx;
      font-size: 20rpx;
      color: white;
      backdrop-filter: blur(10rpx);

      &.active {
        background: rgba(76, 217, 100, 0.9);
      }

      &.inactive {
        background: rgba(153, 153, 153, 0.9);
      }
    }
  }

  .work-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .work-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 12rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .work-desc {
      font-size: 28rpx;
      color: #666;
      line-height: 1.4;
      margin-bottom: 16rpx;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .work-tags {
      margin-bottom: 16rpx;

      .tag {
        display: inline-block;
        padding: 4rpx 12rpx;
        background: #f0f0f0;
        color: #e93323;
        font-size: 24rpx;
        border-radius: 20rpx;
        margin-right: 12rpx;
        margin-bottom: 8rpx;
      }
    }

    .work-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .author-info {
        display: flex;
        align-items: center;

        .avatar {
          width: 40rpx;
          height: 40rpx;
          border-radius: 50%;
          margin-right: 12rpx;
        }

        .author-name {
          font-size: 26rpx;
          color: #666;
        }
      }

      .create-time {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .work-actions {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    margin-left: 16rpx;

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.9);
      }

      .iconfont {
        font-size: 32rpx;
      }

      &.edit-btn {
        background: #f0f9ff;
        color: #0ea5e9;

        &:active {
          background: #e0f2fe;
        }
      }

      &.delete-btn {
        background: #fef2f2;
        color: #ef4444;

        &:active {
          background: #fee2e2;
        }
      }
    }
  }
}

.load-more {
  padding: 40rpx;
  text-align: center;

  .loading-text,
  .no-more-text {
    font-size: 28rpx;
    color: #999;
  }

  .load-more-btn {
    display: inline-block;
    padding: 16rpx 40rpx;
    background: linear-gradient(135deg, #e93323, #f96e29);
    color: white;
    border-radius: 50rpx;
    font-size: 28rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      opacity: 0.8;
    }
  }
}

.publish-modal {
  background: white;
  border-radius: 40rpx 40rpx 0 0;
  max-height: 80vh;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 40rpx 40rpx 20rpx;
    border-bottom: 1px solid #f0f0f0;

    .modal-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      background: #f5f5f5;

      .iconfont {
        font-size: 32rpx;
        color: #666;
      }
    }
  }

  .modal-content {
    padding: 40rpx;
    max-height: 60vh;
    overflow-y: auto;

    .form-item {
      margin-bottom: 40rpx;

      .label {
        font-size: 32rpx;
        color: #333;
        margin-bottom: 20rpx;
        font-weight: 500;
      }

      .input {
        width: 100%;
        padding: 24rpx;
        border: 2rpx solid #e5e5e5;
        border-radius: 16rpx;
        font-size: 30rpx;
        background: #fafafa;

        &:focus {
          border-color: #e93323;
          background: white;
        }
      }

      .textarea {
        width: 100%;
        min-height: 160rpx;
        padding: 24rpx;
        border: 2rpx solid #e5e5e5;
        border-radius: 16rpx;
        font-size: 30rpx;
        background: #fafafa;
        resize: none;

        &:focus {
          border-color: #e93323;
          background: white;
        }
      }

      .image-upload {
        display: flex;
        align-items: center;
        gap: 20rpx;

        .upload-btn {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 160rpx;
          height: 160rpx;
          border: 2rpx dashed #ddd;
          border-radius: 16rpx;
          color: #999;
          font-size: 24rpx;

          .iconfont {
            font-size: 48rpx;
            margin-bottom: 8rpx;
          }
        }

        .preview-image {
          position: relative;
          width: 160rpx;
          height: 160rpx;
          border-radius: 16rpx;
          overflow: hidden;

          image {
            width: 100%;
            height: 100%;
          }

          .remove-image {
            position: absolute;
            top: 8rpx;
            right: 8rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40rpx;
            height: 40rpx;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            color: white;

            .iconfont {
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    gap: 20rpx;
    padding: 20rpx 40rpx 40rpx;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 44rpx;
      font-size: 32rpx;
      font-weight: 500;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #666;
    }

    .confirm-btn {
      background: linear-gradient(135deg, #e93323, #f96e29);
      color: white;

      &.disabled {
        background: #ddd;
        color: #999;
      }
    }
  }
}
.footer-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx;
  .add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 78rpx;
    background: #c9a063;
    border-radius: 39rpx;
    color: white;
    width: 100%;
    .iconfont {
      font-size: 36rpx;
      margin-right: 10rpx;
    }
  }
}
</style>
