// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import { mapGetters } from 'vuex'
import storeHelper from '@/utils/storeHelper.js'

/**
 * 门店信息 mixin
 * 提供门店信息相关的计算属性和方法
 */
export default {
  computed: {
    ...mapGetters(['storeInfo']),
    
    // 门店ID
    storeId() {
      return this.storeInfo.mer_id || ''
    },
    
    // 门店名称
    storeName() {
      return this.storeInfo.mer_name || ''
    },
    
    // 是否有门店信息
    hasStoreInfo() {
      return storeHelper.hasStoreInfo()
    }
  },
  
  methods: {
    /**
     * 设置门店信息
     * @param {Object} storeInfo 门店信息对象
     */
    setStoreInfo(storeInfo) {
      storeHelper.setStoreInfo(storeInfo)
    },
    
    /**
     * 获取门店信息
     * @returns {Object} 门店信息对象
     */
    getStoreInfo() {
      return storeHelper.getStoreInfo()
    },
    
    /**
     * 清除门店信息
     */
    clearStoreInfo() {
      storeHelper.clearStoreInfo()
    },
    
    /**
     * 检查门店信息是否存在，不存在则提示用户
     * @param {String} message 提示信息
     * @returns {Boolean} 是否有门店信息
     */
    checkStoreInfo(message = '请先选择门店') {
      if (!this.hasStoreInfo) {
        uni.showToast({
          title: message,
          icon: 'none'
        })
        return false
      }
      return true
    }
  }
}
