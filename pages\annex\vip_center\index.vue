<template>
  <!-- svip会员模块 -->
	<view class="card_content">
		<view class="card-section" :style="'background-image: url('+domain+'/static/images/vip_header_bg.png)'">
			<view class="header-card" :style="'background-image: url('+domain+'/static/images/svip_section.png)'">
				<view class="header-avatar acea-row">
					<view class="acea-row row-middle">
						<view class="avatar-box on">
							<image class="image" :src="userInfo.avatar ? userInfo.avatar : '/static/images/f.png'"></image>
							<view class="headwear">
								<image :src="`${domain}/static/images/crown.png`"></image>
							</view>
						</view>
						<view class="text acea-row">
							<view class="name">{{userInfo.nickname || ''}}</view>
							<image class="vip" :src="`${domain}/static/images/svip.png`"></image>
						</view>
					</view>
					<view v-if="userInfo.is_svip == 3" class="expire">终身会员</view>
					<view v-else class="expire">{{userInfo.svip_endtime | dateFormat}}到期</view>
				</view>
				<view class="header_count">
					<view class="acea-row row-between vip_save">
						<view class="save_money">
							<text class="name">累计节省(元)</text>
							<view class="money">{{userInfo.svip_save_money}}</view>
						</view>
						<view v-if="userInfo.is_svip != 3" @click="scrollToCard" class="renew_btn">立即续费</view>
					</view>
					<view class="save_list acea-row">
						<scroll-view scroll-x="true" style="white-space: nowrap; display: flex" show-scrollbar="false">
							<view v-for="(item,index) in memberRights" :key="index" class='item' @click="goUrl(item.link)">
								<image class="pic" :src="item.on_pic"></image>
								<view class="text">
									<view class="name">{{item.name}}</view>
									<view class="info line1">{{item.info}}</view>
								</view>
							</view>
						</scroll-view>
					</view>
				</view>
			</view>
		</view>
		<!--卡片下方内容-->
		<view class="vip_center">
			<view v-if="memberCoupons.length > 0" class="coupon-section" :style="{ 'background-image': `url(${domain}/static/images/svip_coupon_bg.png)`}">
				<view class="section-hd">
					<view class="title">会员优惠券</view>
					<text class="desc">会员专享优惠券，全场通用</text>
				</view>
				<scroll-view class="section-bd" scroll-x="true">
					<view v-for="item in memberCoupons" :key="item.coupon_id" class="item"
						:class="{gray: item.svipIssue}" 
						:style="'background-image: url('+domain+'/static/images/svip_coupon.png)'">
						<view class="coupon-count">
							<view class="money">
								<text>¥</text>
								<text class="num">{{item.coupon_price}}</text>
							</view>
							<view class="text">
								<view v-if="item.use_min_price === '0.00'" class="mark">领券立减{{item.coupon_price}}元</view>
								<view v-else class="mark">满{{item.use_min_price | moneyFormat}}可用</view>
							</view>
						</view>
						<navigator hover-class="none" :url="'/pages/columnGoods/goods_coupon_list/index?coupon_id='+item.coupon_id" v-if="item.svipIssue" class="coupon-btn">去使用</navigator>
						<view v-else @click="receiveCoupon(item)" class="coupon-btn">立即领取</view>
					</view>
				</scroll-view>
			</view>
			<!--立即续费-->
			<view v-if="userInfo.is_svip != 3" class="type-section" id="card" :style="{ 'background-image': `url(${domain}/static/images/renew_bg.png)`}">
				<view class="section-hd">
					<view class="title">会员续费</view>
					<text class="desc">续费可继续享受优惠价</text>
				</view>
				<scroll-view class="scroll" scroll-x="true">
					<view v-for="(item,index) in memberType" :key="item.group_data_id" class="item" :class="{on: index === type}"
						@click="checkType(item,index)">
						<view class="title line1">{{item.value && item.value.svip_name}}</view>
						<view class="new">
							<priceFormat :price="item.value && item.value.price | moneyFormat" weight intSize="50" floatSize="50" labelSize="34"></priceFormat>
						</view>
						<view class="old">¥{{item.value && item.value.cost_price | moneyFormat}}</view>
					</view>
				</scroll-view>
				<view class="buy" @click="pay">立即支付</view>
				<view v-if="memberExplain" class="agree">
					<navigator class="link" url="/pages/annex/vip_clause/index" hover-class="none">购买即视为同意<text
						class="mark">《会员用户协议》</text></navigator>
				</view>	
			</view>
			<view v-if="goodsList.length" class="goods-section">
				<view class="section-hd">
					<view class="title">会员专享价</view>
				</view>
				<view class="section-bd acea-row">
					<view v-for="item in goodsList" :key="item.product_id" class="item" @click="goDetail(item.product_id)">
			      <easy-loadimage class="image" mode="widthFix" :image-src="item.image"></easy-loadimage>
						<view class="name line2">
							<text v-if="item.merchant.type_name && item.product_type == 0" class="font-bg-red bt-color">{{item.merchant.type_name}}</text>
							{{item.store_name}}
						</view>
						<view class="svip-price">
							<priceFormat :price="item.svip_price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
							<image :src="`${domain}/static/images/svip.png`"></image>
						</view>
						<view class="shop-price">商城价：¥{{item.price}}</view>
					</view>
				</view>
			</view>
		</view>
		<payment :payMode="payMode" :pay_close="pay_close" :is-call="true" @onChangeFun="onChangeFun"
			:order_id="pay_order_id" :totalPrice="totalPrice"></payment>
		<view v-show="false" v-html="formContent"></view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	let app = getApp();
	import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue';
	import payment from '@/components/payment';
	import { mapGetters } from "vuex";
	import {
		memberCard,
		memberEquity,
		memberCouponLst,
		memberCardDraw,
		memberCardCreate,
		groomList,
		getAgreementApi,
		receiveMemberCoupon
	} from '@/api/user.js';
	import { toLogin } from '@/libs/login.js';
	import { openPaySubscribe } from '@/utils/SubscribeMessage.js';
	import dayjs from '@/plugin/dayjs/dayjs.min.js';
	import { HTTP_REQUEST_URL } from '@/config/app';
	export default {
		components: {
			payment,
      easyLoadimage
		},
		filters: {
			dateFormat: function(value) {
				return dayjs(value).format('YYYY-MM-DD');
			},
			moneyFormat: function(value) {
				return parseFloat(value);
			}
		},
		data() {
			return {
				domain: HTTP_REQUEST_URL,
				memberType: [],
				userInfo: {},
				memberRights: [],
				memberExplain: [],
				memberCoupons: [],
				isGetFree: null,
				popupShow: false,
				account: '',
				password: '',
				goodsList: [],
				pay_order_id: '',
				payMode: [{
					name: '微信支付',
					icon: 'icon-a-ic_wechatpay',
					// #ifdef H5
					value: this.$wechat.isWeixin() ? 'weixin' : 'h5',
					// #endif
					// #ifdef MP
					value: 'routine',
					// #endif
					// #ifdef APP-PLUS
					value: 'weixin',
					// #endif
					title: '微信快捷支付',
					payStatus: app.globalData.pay_weixin_open
					}
					// #ifdef H5 ||APP-PLUS
					,
					{
					name: '支付宝支付',
					icon: 'icon-a-ic_alipay',
					// #ifdef H5 || APP-PLUS
					value: 'alipay',
					// #endif
					// #ifdef MP
					value: 'alipayQr',
					// #endif
					title: '支付宝支付',
					payStatus: app.globalData.alipay_open
					}
					// #endif
				],
				pay_close: false,
				totalPrice: '0',
				formContent: '',
				page: 1,
				limit: 15,
				finished: false,
				loading: false,
				memberEndTime: '',
				// #ifdef H5
				isWeixin: this.$wechat.isWeixin(),
				// #endif
				type: 0,
				svip_type: 1,
				svip: null,
				svipDef: {},
			}
		},
		watch: {
			
		},
		computed: mapGetters(['isLogin']),
		onLoad() {
			this.getCouponLst();
			if (this.isLogin) {
				this.getMemberCard();
				this.memberEquity();
				this.groomList();
			} else {
				toLogin()
			}
		},
		onShow(){
			
		},
		onReachBottom() {
			this.groomList();
		},
		methods: {
			onLunch() {
				this.getMemberCard();
				this.memberEquity();
				this.getCouponLst();
			},
			goUrl(url){
				if(url.indexOf("http") != -1){
					// #ifdef H5
					location.href = url
					// #endif
				}else{
					if(['/pages/goods_cate/goods_cate','/pages/order_addcart/order_addcart','/pages/user/index','/pages/plant_grass/index'].indexOf(url) == -1){
						uni.navigateTo({
							url:url
						})	
					}else{
						uni.switchTab({
							url:url
						})
					}
				}
			},
			// 付费会员数据
			getMemberCard() {
				uni.showLoading({
					title: '正在加载…'
				});
				memberCard().then(res => {
					uni.hideLoading();
					this.memberType = res.data.list;
					this.svipDef = res.data.def;
					this.totalPrice = res.data.def.price.toString();
					this.pay_order_id = res.data.def.group_data_id.toString();
					this.svip_type = res.data.def.svip_type;
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
			},
			// 付费会员权益
			memberEquity() {
				memberEquity().then(res => {
					this.memberRights = res.data.interests;
					this.userInfo = res.data.user;
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
			},
			// 会员优惠券
			getCouponLst() {
				memberCouponLst().then(res => {
					this.memberCoupons = res.data
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
			},
			receiveCoupon(item) {
				let that = this;
				if (that.isLogin === false) {
					toLogin()
				} else {
					receiveMemberCoupon(item.coupon_id).then(res => {
						item.svipIssue = 1
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					}).catch(err => {
						uni.showToast({
							title: err,
							icon: 'none'
						})
					})
				}
			},
			pay() {
				if (this.totalPrice == 0) {
					this.createMemberCard('weixin');
				} else {
					this.pay_close = true;
				}
			},
			payClose: function() {
				this.pay_close = false;
			},
			// 立即购买
			createMemberCard(type) {
				uni.showLoading({
					title: '正在加载…'
				});
				let query = {
					pay_type: type,
					// #ifdef H5
					return_url: location.port ? location.protocol + '//' + location.hostname + ':' + location.port +
						'/pages/annex/vip_paid/index' : location.protocol + '//' + location.hostname +
						'/pages/annex/vip_paid/index'
					// #endif
				};
				let group_id = this.pay_order_id
				// #ifdef MP
				openPaySubscribe().then(() => {
					memberCardCreate(group_id,query).then(res => {
						if (parseFloat(this.totalPrice) > 0) {
							this.callPay(res);
						} else {
							uni.hideLoading();
							return this.$util.Tips({
								title: this.svip_type == 1 ? '成功开启0元试用' : '续费成功',
							}, () => {
								this.onLunch()
							});
						}
					}).catch(err => {
						uni.hideLoading();
						uni.showToast({
							title: err,
							icon: 'none'
						});
					});
				});
				// #endif
				// #ifndef MP
				memberCardCreate(group_id,query).then(res => {
					if (parseFloat(this.totalPrice) > 0) {
						this.callPay(res);
					} else {
						uni.hideLoading();
						return this.$util.Tips({
							title:  this.svip_type == 1 ? '成功开启0元试用' : '续费成功',
						}, () => {
							this.onLunch()
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
				// #endif
			},
			// 调用支付
			callPay(res) {
				let that = this;
				let status = res.data.status,
					orderId = res.data.result.order_id,
					callback_key = res.data.result.pay_key,
					jsConfig = res.data.result.config,
					goPages = '/pages/annx/vip_center/index'
				switch (status) {
					case 'ORDER_EXIST':
					case 'EXTEND_ORDER':
					case 'PAY_ERROR':
					case 'error':
						uni.hideLoading();
						that.payClose();
						return that.$util.Tips({
							title: res.message
						});
						break;
					case 'success':
						uni.hideLoading();
						that.payClose();
						return that.$util.Tips({
							title: res.message,
							icon: 'success'
						});
						break;
					case 'alipay':
					case "alipayQr":
						uni.hideLoading();
						that.payClose();
						uni.navigateTo({
							url: '/pages/order_pay_back/index?keyCode='+callback_key+'&url='+jsConfig+'&type=11',
						})	
						return;
						break;
						// #ifndef MP
					case "wechat":
					case "weixin":
					case "weixinApp":
						jsConfig.timeStamp = jsConfig.timestamp;
						// #ifndef APP-PLUS
						this.$wechat.pay(jsConfig).then(res => {
							uni.hideLoading();
							that.payClose();
							return that.$util.Tips({
								title: res.message,
								icon: 'success'
							});
						}).catch(res => {
							uni.hideLoading();
							if (res.errMsg == 'chooseWXPay:cancel') return that.$util.Tips({
								title: '取消支付'
							});
						})
						// #endif
						// #ifdef APP-PLUS
						let mp_pay_name=''
						if(uni.requestOrderPayment){
							mp_pay_name='requestOrderPayment'
						}else{
							mp_pay_name='requestPayment'
						}
						uni[mp_pay_name]({
							provider: 'wxpay',
							orderInfo: jsConfig,
							success: (e) => {
								uni.hideLoading();
								that.payClose();
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								});
							},
							fail: (e) => {
								uni.hideLoading();
								that.payClose();
								uni.showModal({
									content: "支付失败",
									showCancel: false,
									success: function(res) {}
								})
							},
							complete: () => {
								uni.hideLoading();
							},
						});
						// #endif
						break;
						// #endif
						// #ifdef MP
					case "routine":
						jsConfig.timeStamp = jsConfig.timestamp;
						let mp_pay_name=''
						if(uni.requestOrderPayment){
							mp_pay_name='requestOrderPayment'
						}else{
							mp_pay_name='requestPayment'
						}
						uni[mp_pay_name]({
							...jsConfig,
							success: function(res) {
								uni.hideLoading();
								that.payClose();
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 5,
									url: goPages
								});
							},
							fail: function(e) {
								uni.hideLoading();
								that.payClose();
								return that.$util.Tips({
									title: '取消支付'
								});
							},
						})
						break;
						// #endif
					case "balance":
						uni.hideLoading();
						that.payClose();
						//余额不足
						return that.$util.Tips({
							title: res.message
						}, {
							tab: 5,
							url: goPages
						});
						break;
						// #ifdef H5
					case 'h5':
						let host = window.location.protocol + "//" + window.location.host;
						let url = `${host}/pages/annex/vip_center/index`
						let eUrl = encodeURIComponent(url)
						let jsurl = jsConfig.mweb_url || jsConfig.h5_url
						let locations = `${jsurl}&redirect_url=${eUrl}`
						setTimeout(() => {
							location.href = locations;
						}, 100);
						break;
						// #endif
						// #ifdef APP-PLUS
					case 'alipayApp':
						uni.requestPayment({
							provider: 'alipay',
							orderInfo: jsConfig,
							success: (e) => {
								uni.hideLoading();
								that.payClose();
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								});
							},
							fail: (e) => {
								uni.hideLoading();
								uni.showModal({
									content: "支付失败",
									showCancel: false,
									success: function(res) {}
								})
							},
							complete: () => {
								uni.hideLoading();
							},
						});
						break;
						// #endif
				}
			},
			payCheck: function(type) {
				this.createMemberCard(type);
			},
			onChangeFun: function(e) {
				let opt = e;
				let action = opt.action || null;
				let value = opt.value != undefined ? opt.value : null;
				action && this[action] && this[action](value);
			},
			scrollToCard() {
				const query = uni.createSelectorQuery().in(this);
				query.select('#card').boundingClientRect(data => {
					uni.pageScrollTo({
						scrollTop: data.top
					});
				}).exec();
			},
			checkType(svip,index) {
				this.svipDef = svip.value;
				this.type = index;
				this.svip_type = svip.value.svip_type;
				this.pay_order_id = svip.group_data_id.toString();
				this.totalPrice = svip.value.price.toString();
			},
			goDetail(id) {
				uni.navigateTo({
					url: `/pages/goods_details/index?id=${id}`
				});
			},	
			groomList() {
				if (this.finished || this.loading) {
					return;
				}
				this.loading = true
				groomList({
					page: this.page,
					limit: this.limit
				}).then(res => {
					this.goodsList = this.goodsList.concat(res.data.list);
					this.finished = res.data.list.length < this.limit;
					this.loading = false;
					this.page += 1;
				}).catch(err => {
		
				});
			},
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		}
	}
</script>
<style lang="scss" scoped>
	.card_content{
		background: #F5f5f5;
	}
	.card-section {
		height: 600rpx;
		background-size: cover;
		background-repeat: no-repeat;
		position: relative;
		.header-card{
			width: 750rpx;
			margin: 0 auto;
			background-repeat: no-repeat;
			background-size: 100% 100%;
			padding: 53rpx 0 60rpx;
			position: absolute;
			left: 0;
			top: 64rpx;
			z-index: 10;
			.header-avatar{
				justify-content: space-between;
				align-items: center;
				padding: 0 53rpx;
				.text {
					flex: 1;
					margin-left: 15rpx;
					font-size: 22rpx;
					line-height: 30rpx;
					color: #89735B;
				}
				.name {
					margin-bottom: 2rpx;
					font-weight: bold;
					font-size: 30rpx;
					line-height: 42rpx;
					color: #865622;
				}
			}
			.vip{
				width: 82rpx;
				height: 36rpx;
				position: relative;
				top: 5rpx;
				margin-left: 7rpx;
			}
			.expire {
				font-size: 22rpx;
				color: #865622;
			}
		}
		.avatar-box{
			position: relative;
			.image {
				width: 64rpx;
				height: 64rpx;
				border-radius: 50%;
				border: 3rpx solid rgb(255, 210, 104);
				margin-right: 20rpx;
			}
			.headwear {
				position: absolute;
				right: 18rpx;
				top: -18rpx;
				width: 18rpx;
				height: 18rpx;
				image {
					width: 100%;
					height: 100%;
				}
			}
		}
	}
	.header_count{
		margin-top: 20rpx;
		.vip_save{
			padding: 0 53rpx;
			.save_money{
				color: #666666;
				font-size: 24rpx;
				.money{
					font-size: 58rpx;
					color: #282828;
				}
			}
			.renew_btn{
				width: 160rpx;
				line-height: 60rpx;
				height: 60rpx;
				text-align: center;
				background: #282828;
				border-radius: 30rpx;
				color: #F7E1A6;
				font-size: 24rpx;
				font-weight: bold;
				position: relative;
				top: 30rpx;
			}
		}
	}
	.save_list{
		margin-top: 20rpx;
		padding: 0 40rpx;
		display: flex;
		.item{
			width: 22%;
			display: inline-block;
			text-align: center;
			margin-right: 12rpx;
			&:last-child{
				margin-right: 0;
			}
			.pic{
				width: 84rpx;
				height: 84rpx;
				border-radius: 100%;
			}
			.text{
				margin-top: 10rpx;
				color: #282828;
				.name{
					font-size: 22rpx;
					font-weight: bold;
				}
				.info{
					max-width: 100%;
					color: #666666;
					font-size: 18rpx;
					margin-top: 4rpx;
				}
			}
		}
	}
	.vip_center{
		border-radius: 34rpx 34rpx 0 0;
		background: #F5F5F5;
		padding: 215rpx 20rpx 0;
		position: relative;
		margin-top: -284rpx;
		z-index: 5;
		.coupon-section {
			margin-top: 20rpx;
			background-color: #FFFFFF;
			background-size: 100%;
			background-repeat: no-repeat;
			border-radius: 16rpx;
			.section-bd {
				white-space: nowrap;
				padding: 0 20rpx;
				.item {
					display: inline-flex;
					width: 160rpx;
					height: 190rpx;
					margin: 26rpx 20rpx 30rpx 0;
					background-size: cover;
					background-repeat: no-repeat;
					position: relative;
					.coupon-count{
						width: 160rpx;
						text-align: center;
						margin-top: 36rpx;
					}
					&:last-child {
						margin-right: 0;
					}
					&.gray {
						&::before{
							content: "";
							display: block;
							width: 160rpx;
							height: 190rpx;
							background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKAAAAC+BAMAAABAPRn1AAAAJFBMVEVHcEzvypbsyZXsypTtyZXwzpPtypXsyZXsyZXsyZXsyZXsyZUx55mvAAAAC3RSTlMAJqVDehFfv9vwj03dKpUAAAVNSURBVGje7VjLU9tGGF9Zlm3IRaGZOMAFE/IAXww2kISLzRhaqoubmmcuCu/WF2xKAtUFSEiZ6JKZ0CapLqSdJJ1wQjb2mu+f6+5KNjaTzlTrTk/7O0i7q9Fv9nvu9y1CvFiw8MRMb188Hl/9tpBS1uAFagl+qxJ7cDvjzhIDahietcInGZXhdKxhPq6Pwq0WCKeqK5/J9ia/Z7vVEVLW9HUc4+Z7CFvLVFwNk0fgZI+urQ2flLgFNstZlbxlyybPdgC2tc+yySv0ffxVkr7bLaC8UGarcn6ukuHboHWw5Iy6U5To5WtnNjtsbHARdtoLqjMKOK9p98PtYIWHTzG3IggNf+GLb8XgIQziBVUqFL/06eMcD6F28B4hy3b1LzVu1TfIYxJIxJBiVXUq/Y+f0uk/+i4cmsdvQuddxLI7Z9S+vWx70mKk7vEchCcHPZSEPOS+mtt1HDNrkSUOG0PC9Rmlr9mN5QKXE7ZXFtxRWG3mM4ArTrpPP7gETQbIvjCwofMQntxyf1ts4rMA62M9PGEC11zvOW7mi+JM2ysOwkAl62aCTCOfbayYx8FTnrgrP2YZGkUasw9Wx3Y7y/4iB+Gjs2NZyxOTJOtLQ2gb1M6yDMs8GVvLJ/00QwfrK6OgI7MAGOVsHsJoWkUW+XH1gu8og3JwZOoB4CE0hxDCRFc9db7z6RgKVTK5XZTDKofX+JCiDtadJghvy1O/okB1WisjGWzPjDJ23NDP/vTf9B1lgUpq7gAOZDF4PgL8tnM8+djzCmx+F4XSO6rEiGGOlzrAK6GvlGogfLQP23BY1pIoZBMN3iiiLu+ETmpwBB/b6gBYxfNEiXDVKne/4gkUx7wJh3AnOartmtShTRPjk2OO/O8SXkUKCRVtB8dC5dzBWzKsDsJfKgdh0XVAZYrUH9EV7Vkfbitquh/2UD9POqztUJkisYZM1Q8lc/H8Xkw6IvVDgCvZMD1JU/DE0tE9kvZLuecYv0dU2iAPYYkSSlHYRNoukRkGiiEborBx4Use3eY85fKheaLEHyb8xUABlPVdXsJ2EimEj2ZXP1GiIvlOSWZAKHPhnB5PABLLMuNTvqF169i7UxStJWpS8kx4L2xIsZFSmJk3afEOsA+1KpO8vZdzMESzrMr45KdEzGi1q1aHJcnp4j3BLhIHid5i+1MsMpbSdYOpJD16P+c3STx0V+EmOw+agreDRrdnwrEzoikfOIfoWFONTtfaPRO2lSPUNE6JqTXmq0CSq/jyVWjmim44B9TxJYk5IEOYlmBFxtfY0Sq8XZRxU6f7RGi9uUOeVTkJc6cbTImX+KQIb+MYspkSDy918KMZ92j1Hs0wRH6cv8T3kJg49IbPzsYelXmSinm7tnaNCCybxJkUDsL58+X6bgeYlMoQixuN9LpBLpk7YhdXDQPpxHjvilNH9OroI5fMB/mGWTxes0XgcmfwbzGHw//UkKxxdaOSld+6fKnh7nHL4PNtu9ZESe49kOFo9etZ4CL0Q/6JM7qSZtVCN9gscfQYRb5oOamEHe13AyMMAWtQn8+CjjijZW89xrTpJLB51uWtL5mveQP6PqQKVI2z2Cl1JDIJf9Awb8ZBkolH9snf8kW72BUJw1YLt31gX99v3M9aPgu/tXLfdwMqI09TtdnM3Ui2fk/CiXXAqbVf/hycVq4m7vwce2DZKmoNowA/DSY+3ykMTKoz/fB7q3wkCZoALyeX4vGRu4Q7g1qH3A8u3kyg/wbXP22/qx4WHiMBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQGB/wd/A1K7dDGJQCorAAAAAElFTkSuQmCC");
							background-size: cover;
							background-repeat: no-repeat;
							position: absolute;
							top: 0;
							left: 0;
							z-index: 0;
						}
					}
					.coupon-btn {
						position: absolute;
						bottom: 10rpx;
						color: #8F5C11;
						font-size: 22rpx;
						font-weight: bold;
						display: block;
						width: 100%;
						text-align: center;
						z-index: 3;
					}
					.text {
						max-width: 194rpx;
						margin-top: 10rpx;
						display: block;
					}
					.name {
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
						font-weight: bold;
						font-size: 28rpx;
						line-height: 40rpx;
						color: #5D3324;
					}
					.mark {
						display: inline-block;
						height: 35rpx;
						font-size: 22rpx;
						line-height: 35rpx;
						color: #8F5C11;
					}
					.money {
						max-width: 170rpx;
						text-align: center;
						font-size: 22rpx;
						font-weight: 800;
						color: #8F5C11;
						margin-top: -20rpx;
					}
					.num {
						position: relative;
						font-size: 44rpx;
					}
				}
			}
		}
		.type-section {
		  margin-top: 20rpx;
			border-radius: 20rpx;
			background-color: #FFFFFF;
			padding-bottom: 30rpx;
			background-size: 100%;
			background-repeat: no-repeat;
			.title_bd {
				padding: 26rpx 40rpx 0 0;
				font-size: 24rpx;
				color: #999999;
				.bold {
					display: inline-block;
					margin-right: 14rpx;
					font-weight: bold;
					font-size: 32rpx;
					line-height: 45rpx;
					color: #333333;
					+view {
						display: inline-block;
					}
				}
				.time {
					margin-left: 14rpx;
					font-size: 24rpx;
					color: #AE5A2A;
				}
			}
			.scroll {
				white-space: nowrap;
				padding: 0 30rpx;
			}
			.item {
				display: inline-flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				width: 210rpx;
				height: 240rpx;
				border-radius: 12rpx;
				margin: 32rpx 20rpx 30rpx 0;
				border: 1px solid #CFCFCF;
				line-height: 42rpx;
				&:last-child {
					margin-right: 0;
				}
				&.on {
					border: 3rpx solid #FCC282;
					background-color: #FEF7EC;
					.new {
						color: #DBAA4D;
					}
				}
				.title{
					color: #282828;
					font-weight: bold;
					font-size: 30rpx;
					width: 180rpx;
					text-align: center;
				}
			}
			.new {
				margin-top: 22rpx;
				font-weight: 600;
				font-size: 34rpx;
				color: #E7BE7D;
				.num {
					font-size: 48rpx;
					line-height: 48rpx;
				}
			}
			.old {
				margin-top: 13rpx;
				text-decoration: line-through;
				font-size: 24rpx;
				color: #999999;
			}
			.info {
				margin-top: 13rpx;
				font-size: 24rpx;
			}
			.agree {
				font-size: 22rpx;
				text-align: center;
				color: #999999;
				margin-top: 30rpx;
				.link {
					display: inline-block;
				}
				.mark {
					color: #8A602E;
				}
			}
			.buy {
				height: 80rpx;
				border-radius: 12rpx;
				background: linear-gradient(270deg, #E5BA62 0%, #ECCA7F 51%, #F3D99B 100%);
				font-size: 30rpx;
				font-weight: bold;
				line-height: 80rpx;
				text-align: center;
				color: #865622;
				border-radius: 44rpx;
				margin: 0 30rpx;
			}
			.cash {
				padding-top: 26rpx;
				padding-bottom: 29rpx;
				font-size: 28rpx;
				text-align: center;
				color: #754E19;
			}
		}
		.section-hd {
			padding: 30rpx 30rpx 0;
			.title {
				font-weight: bold;
				font-size: 32rpx;
				line-height: 45rpx;
				color: #333333;
			}
			.desc {
				font-size: 20rpx;
				color: #999999;
			}
		}
	}
	.goods-section {
		margin-top: 20rpx;
		background-color: #ffffff;
		background-image: url("data:image/png;base64,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");
		background-size: 100%;
		background-repeat: no-repeat;
		border-radius: 16rpx;
		padding-bottom: calc(0rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
		padding-bottom: calc(0rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
		.section-hd {
			padding: 20rpx 30rpx 0;
			font-weight: bold;
			font-size: 32rpx;
			line-height: 45rpx;
			color: #333333;
			text {
				color: #999999;
				font-size: 24rpx;
				font-weight: normal;
			}
		}
		.section-bd {
			margin-top: 26rpx;
			.item {
				width: 325rpx;
				padding-bottom: 24rpx;
				margin-left: 20rpx;
			}
			/deep/image, /deep/uni-image, /deep/.easy-loadimage {
				width: 325rpx;
				height: 325rpx;
				border-radius: 16rpx 16rpx 0 0;
			}
			.name {
				margin-top: 10rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				line-height: 37rpx;
			}
			.svip-price {
				margin-top: 6rpx;
				font-size: 28rpx;
				font-weight: bold;
				image {
					width: 65rpx;
					height: 28rpx;
					margin-left: 6rpx;
					position:  relative;
					top: 5rpx;
				}
			}
			.shop-price {
				margin-top: 4rpx;
				font-size: 20rpx;
				color: #666666;
			}
		}
	}
</style>
