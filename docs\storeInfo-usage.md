# 门店信息管理使用指南

## 概述

为了解决项目中多处使用 `uni.setStorageSync('storeInfo', JSON.stringify(this.storeInfo))` 的繁琐问题，我们创建了一套完整的门店信息管理系统，包括：

1. **Vuex 状态管理** - 统一管理门店信息状态
2. **工具类** - 提供便捷的操作方法
3. **Mixin** - 简化页面中的使用

## 核心文件

### 1. Vuex Store (`store/modules/app.js`)

```javascript
// 状态
state: {
  storeInfo: uni.getStorageSync('storeInfo') ? JSON.parse(uni.getStorageSync('storeInfo')) : {},
}

// 变更方法
mutations: {
  SET_STORE_INFO(state, storeInfo) {
    state.storeInfo = storeInfo
    uni.setStorageSync('storeInfo', JSON.stringify(storeInfo))
  },
  CLEAR_STORE_INFO(state) {
    state.storeInfo = {}
    uni.removeStorageSync('storeInfo')
  },
}
```

### 2. 工具类 (`utils/storeHelper.js`)

提供门店信息的基础操作方法：

```javascript
import storeHelper from '@/utils/storeHelper.js'

// 设置门店信息
storeHelper.setStoreInfo(storeInfo)

// 获取门店信息
const storeInfo = storeHelper.getStoreInfo()

// 清除门店信息
storeHelper.clearStoreInfo()

// 检查是否有门店信息
const hasStore = storeHelper.hasStoreInfo()

// 获取门店ID
const storeId = storeHelper.getStoreId()

// 获取门店名称
const storeName = storeHelper.getStoreName()
```

### 3. Mixin (`mixins/storeInfo.js`)

为页面提供门店信息相关的计算属性和方法：

```javascript
import storeInfoMixin from '@/mixins/storeInfo.js'

export default {
  mixins: [storeInfoMixin],
  // 现在可以直接使用：
  // this.storeInfo - 门店信息对象
  // this.storeId - 门店ID
  // this.storeName - 门店名称
  // this.hasStoreInfo - 是否有门店信息
}
```

## 使用方法

### 方法一：使用 Mixin（推荐）

```javascript
// 在页面中引入 mixin
import storeInfoMixin from '@/mixins/storeInfo.js'

export default {
  mixins: [storeInfoMixin],
  
  onLoad(options) {
    // 设置门店信息
    if (options.store) {
      const storeInfo = JSON.parse(options.store)
      this.setStoreInfo(storeInfo)
    }
  },
  
  methods: {
    goToStylist() {
      // 直接使用门店信息，无需手动存储
      uni.navigateTo({
        url: `/pages/hairdressing/hairstylist/index?mer_id=${this.storeId}`
      })
    },
    
    someAction() {
      // 检查门店信息
      if (!this.checkStoreInfo('请先选择门店')) {
        return
      }
      // 执行业务逻辑
    }
  }
}
```

### 方法二：使用工具类

```javascript
import storeHelper from '@/utils/storeHelper.js'

export default {
  onLoad(options) {
    if (options.store) {
      const storeInfo = JSON.parse(options.store)
      storeHelper.setStoreInfo(storeInfo)
    }
  },
  
  methods: {
    someMethod() {
      const storeInfo = storeHelper.getStoreInfo()
      const storeId = storeHelper.getStoreId()
      // 使用门店信息
    }
  }
}
```

### 方法三：直接使用 Vuex

```javascript
import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters(['storeInfo'])
  },
  
  methods: {
    setStore(storeInfo) {
      this.$store.commit('SET_STORE_INFO', storeInfo)
    }
  }
}
```

## 迁移指南

### 旧代码
```javascript
// 设置门店信息
uni.setStorageSync('storeInfo', JSON.stringify(this.storeInfo))

// 获取门店信息
const storeInfo = JSON.parse(uni.getStorageSync('storeInfo'))
```

### 新代码
```javascript
// 使用 mixin
this.setStoreInfo(storeInfo)
const storeInfo = this.getStoreInfo()

// 或使用工具类
storeHelper.setStoreInfo(storeInfo)
const storeInfo = storeHelper.getStoreInfo()
```

## 优势

1. **统一管理** - 所有门店信息通过 Vuex 统一管理
2. **自动同步** - Vuex 状态变化自动同步到本地存储
3. **简化使用** - 通过 mixin 提供便捷的计算属性和方法
4. **类型安全** - 提供完整的方法封装，减少错误
5. **易于维护** - 集中管理，便于后续修改和扩展

## 注意事项

1. 使用 mixin 时，页面会自动获得门店信息相关的计算属性
2. 门店信息的变更会自动同步到本地存储，无需手动调用 `uni.setStorageSync`
3. 建议优先使用 mixin 方式，代码更简洁
4. 如果只需要简单的存取操作，可以使用工具类
