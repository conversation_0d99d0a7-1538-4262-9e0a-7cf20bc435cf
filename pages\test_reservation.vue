<template>
  <view class="test-page">
    <view class="test-button" @click="goToReservationList">
      测试预约列表页面
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    goToReservationList() {
      uni.navigateTo({
        url: '/pages/hairdressing/my_reservation/list'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 100rpx;
  
  .test-button {
    background: linear-gradient(135deg, #d8b488 0%, #c19a5c 100%);
    color: #fff;
    padding: 30rpx;
    border-radius: 20rpx;
    text-align: center;
    font-size: 32rpx;
  }
}
</style>
