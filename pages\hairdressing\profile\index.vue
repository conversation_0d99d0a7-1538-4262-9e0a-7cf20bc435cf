<template>
  <view class="profile-page">
    <!-- 顶部提示 -->
    <view class="profile-tip">
      <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/75da9202508011742187555.png" class="tip-icon" />
      <text>完善个人资料可获得{{ signInConfig.info | getNumber }}个成长值</text>
    </view>
    <!-- 资料卡片 -->
    <view class="profile-card">
      <!-- 头像部分 -->
      <view class="avatar-section">
        <button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
          <image :src="userInfo.avatar || 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/ae331202508011741087381.png'" class="avatar" />
          <view class="avatar-camera">
            <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/fb8b7202508022110339552.png" class="camera-icon" />
          </view>
        </button>
      </view>
      <view class="info-list">
        <!-- 昵称 -->
        <view class="info-item">
          <text class="label">昵称</text>
          <input
            type="nickname"
            class="value nickname-input"
            placeholder="请完善昵称"
            :value="userInfo.nickname"
            @blur="onNicknameChange"
          />
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>

        <!-- ID号 -->
        <view class="info-item">
          <text class="label">ID号</text>
          <text class="value">{{ userInfo.uid }}</text>
          <text class="iconfont icon-ic_copy" @tap="copyUid"></text>
        </view>

        <!-- 手机号 -->
        <view class="info-item">
          <text class="label">电话</text>
          <input type="text" class="value" :value="userInfo.phone" disabled />
          <text class="iconfont icon-ic_lock"></text>
        </view>

        <!-- 性别选择 -->
        <view class="info-item">
          <text class="label">性别</text>
          <picker
            :value="genderIndex"
            :range="genderArray"
            @change="onGenderChange"
            class="value-picker"
          >
            <view class="value">
              {{ genderArray[genderIndex] }}
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </picker>
        </view>

        <!-- 生日 -->
        <view class="info-item">
          <text class="label">生日</text>
          <picker
            mode="date"
            :value="birthday"
            start="1900-01-01"
            end="2025-12-31"
            @change="onBirthdayChange"
            class="value-picker"
          >
            <view class="value">
              {{ birthday }}
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </picker>
        </view>
      </view>
    </view>

    <button class="save-btn center" hover-class="button-hover" @tap="saveUserInfo">保存</button>
  </view>
</template>

<script>
import { getUserSetting, userSettingEdit, editAvatar } from '@/api/user.js'

export default {
  data() {
    return {
      userInfo: {},
      genderArray: ['男', '女'],
      genderIndex: 0,
      birthday: '2001-05-18',
    }
  },
  computed: {
    // 获取特定 ID 的服务配置
    signInConfig() {
      return this.$store.getters.getServiceConfigById(1345) // 获取每日签到配置
    },
  },

  onLoad(options) {
    this.getUserSetting()
  },
  filters: {
    getNumber(value) {
      return value ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0'
    },
  },
  methods: {
    /**
     * 获取用户详情
     */
    getUserSetting() {
      getUserSetting().then((res) => {
        this.userInfo = res.data
        // 设置性别索引 1为男，0为女
        this.genderIndex =
          res.data.extend_info.find((item) => item.field === 'sex')?.value === 1 ? 0 : 1
        // 设置生日
        this.birthday =
          res.data.extend_info.find((item) => item.field === 'birthday')?.value || '2001-05-18'
      })
    },

    // 选择头像
    onChooseAvatar(e) {
      const { avatarUrl } = e.detail
      this.$util.uploadImgs(
        'upload/image',
        avatarUrl,
        (res) => {
          console.log('🚀 ~ onChooseAvatar ~ res:', res)
          this.userInfo.avatar = res.data.path
        },
        (err) => {
          console.log(err)
        },
      )
    },

    // 昵称变更
    onNicknameChange(e) {
      this.userInfo.nickname = e.detail.value
    },

    // 复制ID
    copyUid() {
      uni.setClipboardData({
        data: this.userInfo.uid.toString(),
        success: () => {
          uni.showToast({
            title: 'ID已复制',
            icon: 'none',
          })
        },
      })
    },

    // 性别选择
    onGenderChange(e) {
      this.genderIndex = e.detail.value
      this.userInfo.extend_info.forEach((item) => {
        if (item.field === 'sex') {
          // 1 代表男，2 代表女，0 代表保密
          item.value = Number(this.genderIndex) + 1
        }
      })
    },

    // 生日选择
    onBirthdayChange(e) {
      this.birthday = e.detail.value
      this.userInfo.extend_info.forEach((item) => {
        if (item.field === 'birthday') {
          item.value = this.birthday
        }
      })
    },

    // 保存资料
    async saveUserInfo() {
      uni.showLoading({
        title: '保存中...',
      })
      try {
        const userInfo = {
          avatar: this.userInfo.avatar,
          nickname: this.userInfo.nickname,
        }
        const result = await editAvatar(userInfo)
        if (result.status === 200) {
          userSettingEdit(this.userInfo)
            .then((res) => {
              this.$util.Tips(
                {
                  title: '资料已保存',
                },
                {
                  tab: 3,
                },
              )
            })
            .catch((err) => {
              this.$util.Tips(
                {
                  title: err || '保存失败',
                },
                {
                  tab: 3,
                },
              )
            })
        } else {
          this.$util.Tips(
            {
              title: '保存失败',
            },
            {
              tab: 3,
            },
          )
        }
      } catch (error) {
        console.log('🚀 ~ saveUserInfo ~ error:', error)
      } finally {
        uni.hideLoading()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.profile-page {
  background: #fafafa;
  min-height: 100vh;
  padding: 0 30rpx;
}
.profile-tip {
  display: flex;
  align-items: center;
  padding: 26rpx 0;
  font-weight: 500;
  font-size: 24rpx;
  color: #c9a063;
  .tip-icon {
    width: 32rpx;
    height: 32rpx;
    margin-right: 20rpx;
  }
}
.profile-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 32rpx 0 0 0;
}
.avatar-section {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
  .avatar-btn {
    position: relative;
  }
  .avatar {
    width: 118rpx;
    height: 118rpx;
    border-radius: 50%;
    background: #f5f5f5;
  }
  .avatar-camera {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 38rpx;
    height: 38rpx;
    background: #ffffff;
    box-shadow: 0rpx 3rpx 4rpx 0rpx rgba(0, 0, 0, 0.08);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    .camera-icon {
      width: 20rpx;
      height: 20rpx;
    }
  }
}
.info-list {
  padding: 0 40rpx 32rpx 40rpx;
  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 28rpx 0 !important;
    border-bottom: 1px solid #f5f5f5;
    .label {
      font-size: 32rpx;
      color: #222;
      display: inline-block;
      width: 120rpx;
    }
    .value {
      font-size: 32rpx;
      color: #222;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .nickname-input {
      text-align: left;
    }
    .placeholder {
      color: #ccc;
    }
  }
  .iconfont {
    color: #cccccc;
    font-weight: 500;
  }
  .info-item:last-child {
    border-bottom: none;
  }
}
.save-btn {
  width: 100%;
  margin-top: 116rpx;
  height: 90rpx;
  background: #c49e67;
  color: #fff;
  font-size: 30rpx;
  border-radius: 46rpx;
  border: none;
}
.value-picker {
  flex: 1;
  // text-align: right;
}
</style>
