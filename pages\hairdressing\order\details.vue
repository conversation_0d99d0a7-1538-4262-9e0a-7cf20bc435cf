<template>
  <view class="refund-page">
    <!-- 退款状态 -->
    <view class="refund-status">
      <!--  <view class="">
        <text class="iconfont icon-a-ic_CompleteSelect"></text>
        <text class="status-text">退款成功</text>
      </view> -->
      <!-- 未核销 -->
    </view>
    <view class="refund-status-2">请在2026-05-08 23:59:00前到店使用</view>
    <!-- 退款金额卡片 -->
    <view class="card refund-amount-card">
      <view class="row">
        <text class="label gray">现金退回至余额钱包</text>
        <text class="value price">￥220</text>
      </view>
      <view class="row">
        <text class="label gray">退回御享值</text>
        <text class="value">600</text>
      </view>
    </view>

    <!-- 门店信息 -->
    <view class="card shop-card">
      <view class="shop-title-row">
        <text class="shop-title">御剪理享金牛店</text>
        <text class="iconfont icon-ic_rightarrow"></text>
      </view>
      <view class="shop-address">
        金牛区营门口街道银河北街232号
        <text class="iconfont icon-icon_copy copy-btn" @tap="copyAddress"></text>
        <text class="distance">距你2.5km</text>
      </view>
    </view>

    <!-- 服务项目卡片 -->
    <view class="card service-card">
      <view class="section-title">
        服务项目
        <button type="button" class="section-btn" hover-class="btnHoverClass">去预约</button>
      </view>
      <view class="service-info">
        <image
          class="service-img"
          src="https://dummyimage.com/98x98/3c9cff/fff"
          mode="aspectFill"
        />
        <view class="service-detail">
          <view class="service-title-row">
            <text class="service-title">大牌烫发/染发二选一（含剪发）套餐</text>
            <view class="service-price">
              <text class="price">￥226</text>
              <text class="origin">￥326</text>
              <text class="service-count">×1</text>
            </view>
          </view>
        </view>
      </view>
      <view class="service-extra">
        <view class="row">
          <text class="label">发型师</text>
          <view class="stylist">
            <image class="avatar" src="https://avatars.githubusercontent.com/u/3369400?v=4" />
            <text class="name">郑老师</text>
            <roleTagsVue />
          </view>
        </view>
        <view class="row">
          <text class="label">预约时间</text>
          <text class="value">2025-05-27 15:00:00</text>
        </view>
        <view class="row-code">
          <text class="label">券码：</text>
          <text class="value">234567897</text>
          <text class="status">待使用</text>
          <button class="btn" hover-class="btnHoverClass">申请退款</button>
        </view>
        <view class="code-wrap">
          <zb-code
            ref="qrcode"
            :show="codeShow"
            :cid="cid"
            :val="val"
            :size="size"
            :unit="unit"
            :background="background"
            :foreground="foreground"
            :pdground="pdground"
            :icon="icon"
            :iconSize="iconsize"
            :onval="onval"
            :loadMake="loadMake"
            @result="qrR"
          />
        </view>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="card orderinfo-card">
      <view class="section-title">订单信息</view>
      <view class="orderinfo-row">
        <text class="label">退款编号</text>
        <text class="value">1212233333</text>
      </view>
      <view class="orderinfo-row">
        <text class="label">下单时间</text>
        <text class="value">2024-05-10 12:00:00</text>
      </view>
      <view class="orderinfo-row">
        <text class="label">退款时间</text>
        <text class="value">2024-05-12 12:00:00</text>
      </view>
    </view>

    <!-- 再来一单按钮 -->
    <view class="footer">
      <button class="btn-primary" hover-class="btnHoverClass" @tap="reorder">再来一单</button>
    </view>
  </view>
</template>

<script>
import roleTagsVue from '@/components/roleTags/roleTags.vue'
import zbCode from '@/components/zb-code/zb-code.vue'
export default {
  components: { roleTagsVue, zbCode },
  data() {
    return {
      codeShow: true,
      cid: '',
      val: '',
      size: 200,
      unit: 'rpx',
      background: '#ffffff',
      foreground: '#000000',
      pdground: '#ffffff',
      icon: '',
      iconsize: 40,
      onval: '',
      loadMake: false,
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    copyAddress() {
      uni.setClipboardData({ data: '金牛区营门口街道银河北街232号' })
    },
    reorder() {
      // 跳转到下单页面
    },
  },
}
</script>

<style scoped lang="scss">
.refund-page {
  padding: 30rpx 30rpx 120rpx;
}
.card {
  padding: 40rpx 20rpx;
  background-color: white;
  border-radius: 30rpx;
}
.refund-status {
  display: flex;
  align-items: center;
  justify-content: center;
  .iconfont {
    font-size: 38rpx;
    color: #d19e58;
    margin-right: 12rpx;
  }
}
.refund-status-2 {
  font-weight: 500;
  font-size: 26rpx;
  color: #333333;
}

.status-text {
  font-size: 32rpx;
  color: #d0ac6e;
  font-weight: bold;
}

.refund-amount-card {
  margin-top: 40rpx;
  .row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:first-child {
      margin-bottom: 40rpx;
    }
  }
  .label {
    color: #999999;
    font-size: 28rpx;
  }
  .value {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
  }
}

.shop-card {
  margin-top: 10rpx;
  .iconfont {
    font-size: 28rpx;
    color: #999999;
    margin-left: 10rpx;
  }
  .shop-title-row {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 600;
    color: #222;
  }
  .shop-title-row {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    font-weight: 600;
    color: #222;
  }
  .shop-address {
    color: #888;
    font-size: 26rpx;
    margin-top: 12rpx;
    display: flex;
    align-items: center;
  }
  .copy-btn {
    font-size: 28rpx;
    color: #999;
    margin-left: 10rpx;
  }

  .distance {
    color: #d0ac6e;
    margin-left: 8rpx;
    font-size: 26rpx;
  }
}

.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .section-btn {
    border-radius: 30rpx;
    border: 1px solid #c9a063;
    font-weight: 400;
    font-size: 20rpx;
    color: #c9a063;
    padding: 10rpx 28rpx;
  }
}
.service-card {
  margin-top: 10rpx;
}

.orderinfo-card {
  margin-top: 10rpx;
}

.service-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 18rpx;
}

.service-img {
  width: 98rpx;
  height: 98rpx;
  border-radius: 12rpx;
  margin-right: 18rpx;
}

.service-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.service-title-row {
  display: flex;
  // align-items: center;
  justify-content: space-between;
}

.service-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  flex: 1;
}

.service-price {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  .price {
    color: #333;
    font-size: 28rpx;
    font-weight: 700;
  }
  .origin {
    color: #999;
    font-size: 22rpx;
    text-decoration: line-through;
  }
  .service-count {
    font-size: 26rpx;
    color: #666;
    margin-top: 24rpx;
  }
}

.service-extra {
  margin-top: 14rpx;
  .row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40rpx;
  }
  .label {
    font-weight: 400;
    font-size: 26rpx;
    color: #333333;
  }
  .value {
    color: #222;
    font-size: 28rpx;
  }
  .stylist {
    display: flex;
    align-items: center;
  }
  .name {
    font-weight: 300;
    font-size: 26rpx;
    color: #333333;
  }
  .avatar {
    width: 28rpx;
    height: 28rpx;
    border-radius: 50%;
    margin-right: 12rpx;
  }
}

.row-code {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 26rpx;
  border-top: 1rpx dashed #e6e6e6;
  padding-top: 48rpx;
  color: #333333;
  .status {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    margin-left: 20rpx;
  }
  .btn {
    width: fit-content;
    font-weight: 400;
    font-size: 20rpx;
    color: #999999;
    border-radius: 30rpx;
    border: 1px solid #999999;
    padding: 10rpx 20rpx;
    margin-left: auto;
  }
}

.orderinfo-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  .label {
    color: #333;
    font-weight: 400;
    font-size: 28rpx;
  }
  .value {
    color: #666;
    font-size: 28rpx;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10rpx 24rpx;
  background: white;
  z-index: 20;
}

.btn-primary {
  width: 100%;
  border-radius: 40rpx;
  background: #c9a063;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  padding: 24rpx 0;
  border: none;
}
</style>
