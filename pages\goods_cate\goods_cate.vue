<template>
	<view>
		<skeleton :show="showSkeleton" :isNodes="isNodes" ref="skeleton" loading="chiaroscuro" selector="skeleton"
			bgcolor="#FFF"></skeleton>
		<view class='productSort' :style="{visibility: showSkeleton ? 'hidden' : 'visible', height: winHeight+ 'px'  }">
			<view class='header acea-row row-center-wrapper'>
				<navigator url="/pages/columnGoods/goods_search/index" class='acea-row row-between-wrapper input skeleton-rect' hover-class="none">
					<text class='iconfont icon-ic_search'></text>
					<view class="input-box">点击搜索商品信息</view>
				</navigator>
			</view>
			<view class="con-box" :style="viewColor">	
				<view class='aside'>	
					<scroll-view scroll-y="true" style="height: 100%; overflow: hidden;" scroll-with-animation='true'>
						<view v-for="(item,index) in productList" class="item-count">
							<view class='item acea-row row-center-wrapper' :class='index==navActive?"on":""' v-if='item.children && item.children.length > 0'
							 :key="item.store_category_id" @click='tap(item,index)' :id='"sort"+index'>
							 <view class="top-ra">
							 	<text></text>
							 </view>
							 <text class="item_text skeleton-rect">{{item.cate_name}}</text>
							 <view class="bottom-ra">
							 	<text></text>
							 </view>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class='conter'>
					<scroll-view scroll-y="true" style="height: 100%; overflow: hidden;" scroll-with-animation='true'>
						<view>
							<view class='listw' v-for="(item,index) in cateList" :key="item.store_category_id">
								<view class='title acea-row'>
									<view v-if="hotList.length > 0 && navActive == 0" class='name skeleton-rect'>
										<image class="list_title" mode="heightFix" :src="domain+'/static/diy/recommend_cate'+keyColor+'.png'"></image>
									</view>
									<view v-else class='name skeleton-rect'>{{item.cate_name}}</view>					
								</view>
								<view class='list acea-row'>
									<block v-for="(itemn,indexn) in item.children" :key="itemn.store_category_id">				
										<navigator hover-class='none' :url="'/pages/columnGoods/goods_list/index?id='+itemn.store_category_id+'&title='+itemn.cate_name"
										 class='item acea-row row-column row-middle'>
											<view class='picture skeleton-rect'>
												<!-- <easy-loadimage mode="widthFix" :scroll-top="scrollTop" :image-src="itemn.pic"></easy-loadimage> -->
												<image mode="widthFix" :src="itemn.pic"></image>
											</view>
											<view class='name line1'>{{itemn.cate_name}}</view>
										</navigator>
									</block>
								</view>
							</view>
						</view>
						<view :style='"height:"+(height-300)+"rpx;"'></view>
					</scroll-view>
				</view>
			</view>
			<!-- #ifndef H5 -->
			<passwordPopup></passwordPopup>
			<!-- #endif -->
		</view>
		<!--自定义底部tab栏-->
		<customTab :newData="newData" :activeRouter="activeRouter"></customTab>
	</view>
</template>
<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	let app = getApp();
	import { getCategoryList } from '@/api/store.js';
	import { getNavigation } from '@/api/public.js'
	import { configMap } from '@/utils/index';
	// #ifndef H5
	import passwordPopup from '@/components/passwordPopup';
	// #endif
	import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue';
	import customTab from '@/components/customTab';
	import { mapGetters } from "vuex";
	import { HTTP_REQUEST_URL } from '@/config/app';
	export default {
		components: {
			easyLoadimage,
			customTab,
			// #ifndef H5
			passwordPopup,
			// #endif
		},
		computed: configMap({navigation: {}},mapGetters(['viewColor','keyColor','isLogin','cartNum'])),
		data() {
			let active = 0;
			return {
				domain: HTTP_REQUEST_URL,
				showSkeleton: true, //骨架屏显示隐藏
				isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
				scrollTop: 0,
				navlist: [],
				hotList: [], //推荐分类
				productList: [
					{cate_name: 'skeleton',store_category_id: 0,
					children:[
						{cate_name: 'skeleton',store_category_id: 10,children:[
							{cate_name: '',store_category_id: 101,},
							{cate_name: '',store_category_id: 102,},
							{cate_name: '',store_category_id: 103,},
							{cate_name: '',store_category_id: 104,},
							{cate_name: '',store_category_id: 105,},
							{cate_name: '',store_category_id: 106,}
						]}]
					},
					{cate_name: 'skeleton',store_category_id: 2,
					children:[{cate_name:'skeleton',store_category_id: 30}]},
					{cate_name: 'skeleton',store_category_id: 3,
					children:[{cate_name:'skeleton',store_category_id: 31}]},
					{cate_name: 'skeleton',store_category_id: 4,
					children:[{cate_name:'skeleton',store_category_id: 32}]},
					{cate_name: 'skeleton',store_category_id: 5,
					children:[{cate_name:'skeleton',store_category_id: 33}]},
					{cate_name: 'skeleton',store_category_id: 6,
					children:[{cate_name:'skeleton',store_category_id: 34}]},
					{cate_name: 'skeleton',store_category_id: 7,
					children:[{cate_name:'skeleton',store_category_id: 35}]},
					{cate_name: 'skeleton',store_category_id: 8,
					children:[{cate_name:'skeleton',store_category_id: 36}]},
					{cate_name: 'skeleton',store_category_id: 9,
					children:[{cate_name:'skeleton',store_category_id: 37}]},
					{cate_name: 'skeleton',store_category_id: 10,
					children:[{cate_name:'skeleton',store_category_id: 38}]},
					{cate_name: 'skeleton',store_category_id: 11,
					children:[{cate_name:'skeleton',store_category_id: 39}]},
				],
				cateList: [
					{cate_name: 'skeleton',store_category_id: 10,children:[
						{cate_name: '',store_category_id: 101,},
						{cate_name: '',store_category_id: 102,},
						{cate_name: '',store_category_id: 103,},
						{cate_name: '',store_category_id: 104,},
						{cate_name: '',store_category_id: 105,},
						{cate_name: '',store_category_id: 106,}
					]},	
				],
				navActive: 0,
				activceCate: active,
				number: "",
				height: 0,
				hightArr: [],
				winHeight: 0,
				pidIndex: 0,
				intoindex: "",
				pid: '',
				newData: {},
				activeRouter: '',
			}
		},
		onLoad(options) {
			let that = this	
			that.getAllCategory();
			uni.getSystemInfo({
				success: function(res) {
					that.winHeight = res.windowHeight - 2
				},
			});
			// #ifdef H5
			document.body.addEventListener('touchmove', function(event) {
				if (that.$route.path == '/pages/goods_cate/goods_cate') {
					event.preventDefault();
				}
			}, {
				passive: false
			});
			// #endif
		},
		/**
		 * 用户点击右上角分享
		 */
		// #ifdef MP
		onShareAppMessage: function() {
			wx.showShareMenu({
				withShareTicket: true,
				menus: ['shareAppMessage', 'shareTimeline']
			})
			return {
				path: '/pages/goods_cate/goods_cate?activceCate=' + this.productList[this.navActive] ? this.productList[this.navActive].store_category_id : 0
			}
		},
		onShareTimeline: function() {
			return {
				query: {
					activceCate: this.productList[this.navActive] ? this.productList[this.navActive].store_category_id : 0,
				}
			}
		},
		// #endif
		onShow() {
			let that = this
			let routes = getCurrentPages();
			let curRoute = routes[routes.length - 1].route
			that.activeRouter = '/' + curRoute	
			that.$util.getCartNum(that.isLogin,null)
			that.getNav()
		},
		onHide() {},
		onReady() {
			this.isNodes++;
		},	
		methods: {
			getNav() {
				getNavigation().then(res => {
					this.newData = res.data
					if (this.newData.status && this.newData.status.status) {
						uni.hideTabBar()
					} else {
						uni.showTabBar()
					}
				})	
			},
			tap: function(item,index) {
				this.cateList= item['children'];
				this.navActive = index;
			},
			getAllCategory: function() {
				let that = this;
				let value = ""
				that.pidIndex = 0;
				getCategoryList().then(res => {
					that.productList = res.data.list;
					that.hotList = res.data.hot;
					if(that.hotList.length>0){
						let hot = {
							cate_name: '推荐分类',store_category_id: 0,
							children: [
								{
									cate_name:'推荐分类',
									store_category_id: 1,
									children:that.hotList
								}
							]
						}
						that.productList.unshift(hot)	
					}
					that.cateList = that.productList[0]['children']
					that.getCateFrom(that.productList)
					uni.stopPullDownRefresh(); //结束下拉刷新
					setTimeout(() => {
						that.showSkeleton = false
					}, 500)
					const index = uni.getStorageSync('storeIndex');
					if(index){
						uni.removeStorageSync('storeIndex');
						for(var i = 0; i < that.productList.length; i++){
							if(that.productList[i].store_category_id == index ){
								that.tap(that.productList[i], i)
								return;
							}
						}
					}
				})
			},
			//获取首页分类来源
			getCateFrom: function(arr) {
				arr.map((item, index) => {
					if ((this.activceCate && item.store_category_id == this.activceCate)) {
						this.pidIndex = index
						this.navActive = index
						this.tap(item,index)
						return;
					}
				})
			},
		},
		onPullDownRefresh: function(){
			this.getAllCategory();
		},
		// 滚动监听
		onPageScroll({scrollTop}) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			this.scrollTop = scrollTop;
		}
	}
</script>
<style scoped lang="scss">
	.productSort {
		display: flex;
		flex-direction: column;
		width: 100%;
		.con-box {
			flex: 1;
			display: flex;
			overflow: hidden;
		}
	}
	.productSort .header {
		width: 100%;
		height: 96rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #f5f5f5;
	}
	.productSort .header .input {
		width: 700rpx;
		height: 60rpx;
		background-color: #f5f5f5;
		border-radius: 50rpx;
		box-sizing: border-box;
		padding: 0 25rpx;
	}
	.productSort .header .input .iconfont {
		font-size: 35rpx;
		color: #999;
	}
	.productSort .header .input .placeholder {
		color: #cccccc;
	}
	.productSort .header .input .input-box {
		display: flex;
		align-items: center;
		font-size: 26rpx;
		height: 100%;
		width: 597rpx;
		color: #CCCCCC;
	}
	.productSort .aside {
		background-color: #fff;
		overflow-y: auto;
		overflow-x: hidden;
		width: 180rpx;
		height: 100%;
		overflow: hidden;
		.active-item {
			position: relative;
			&::before,&::after {
				content: "";
				display: block;
				width: 24rpx;
				height: 24rpx;
				background: #f5f5f5;
				border-radius: 100%;
				position: absolute;
				right: 0;
			}
			&::before{
				top: 0;
			}
			&::after{
				bottom: 0;
			}
		}
	}
	.productSort .aside .item {
		height: 100rpx;
		width: 100%;
		font-size: 26rpx;
	}
	.productSort .aside .item_text {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	.productSort .aside .item.on {
		background-color: #f7f7f7;
		width: 100%;
		text-align: center;
		color: var(--view-theme);
		font-weight: bold;
		position: relative;
		&::before {
			content: "";
			display: block;
			width: 4rpx;
			height: 28rpx;
			background-color: var(--view-theme);
			position: absolute;
			left: 0;
			top: 50%;
			margin-top: -14rpx;
		}
		.top-ra{
			width: 32rpx;
			height: 32rpx;
			background-color: #f5f5f5;
			position: absolute;
			right: 0;
			top:-32rpx;
			text{
				position: absolute;
				top:0;
				right: 0;
				width: 100%;
				height: 100%;
				background-color: #fff;
				display: inline-block;
				border-radius: 0 0 24rpx 0;
			}
		}
		.bottom-ra{
			width: 32rpx;
			height: 32rpx;
			background-color: #f5f5f5;
			position: absolute;
			right: 0;
			bottom:-32rpx;
			text{
				position: absolute;
				top:0;
				right: 0;
				width: 100%;
				height: 100%;
				background-color: #fff;
				display: inline-block;
				border-radius: 0 24rpx 0 0;
			}
		}
	}
	.productSort .conter {
		flex: 1;
		height: 100%;
		padding: 0 14rpx 0 20rpx;
		background-color: #f7f7f7;
	}
	.productSort .conter .listw .title {
		height: 100rpx;
		align-items: center;
	}
	.productSort .conter .listw .title .line {
		width: 100rpx;
		height: 2rpx;
		background-color: #f0f0f0;
	}
	.productSort .conter .listw .title .name {
		font-size: 26rpx;
		margin: 0 30rpx;
		font-weight: bold;
	}
	.productSort .conter .listw .title .list_title,
	.productSort .conter .listw .title uni-image,
	.productSort .conter .listw .title image{
		width: 110rpx;
		height: 26rpx;
		border-radius: 0;
	}
	.productSort .conter .list {
		flex-wrap: wrap;
		background: #fff;
		border-radius: 24rpx;
		padding-bottom: 26rpx;
	}
	.productSort .conter .list .item {
		width: 174rpx;
		margin-top: 26rpx;
	}
	.productSort .conter .list .item .picture,
	.productSort .conter uni-image,
	.productSort .conter .list .item image,
	/deep/.easy-loadimage{
		width: 110rpx;
		height: 110rpx;
	}
	.productSort .conter .list .item .name {
		font-size: 22rpx;
		height: 56rpx;
		line-height: 56rpx;
		width: 120rpx;
		text-align: center;
		margin-top: 30rpx;
	}	
</style>