// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c)  https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
.main {
	padding: 0 20rpx;
}
.spike-bd {
	margin-bottom: 20rpx;
	border-radius: 16rpx;
	padding: 0 20rpx 0 10rpx;
	display: flex;
	position: relative;
	justify-content: space-between;
	.title-img{
		width: 136rpx;
		height: 36rpx;
	}
	.title {
		font-weight: bold;
		color: #282828;
		font-size: 0;
		.title-img{
			width: 136rpx;
			height: 36rpx;
		}
	}
	.more-btn {
		color: #282828;
		font-size: 24rpx;
		.iconfont {
			color: #999999;
			font-size: 22rpx;
			margin-left: 8rpx;
		}
	}
}
.colum0{
	white-space: nowrap; 
	display: flex;
}
.combination-item {
	/* #ifdef MP  */
	width: 294rpx;
	height: 140rpx;
	/* #endif */
	/* #ifdef H5 || APP-PLUS */
	width: calc(50% - 10rpx);
	height: 180rpx;
	/* #endif */
	display: inline-block;
	background-size: 100%;
	position: relative;
	background-repeat: no-repeat;
	border-radius: 16rpx;
	.img-box {
		width: 122rpx;
		height: 122rpx;
		position: absolute;
		/deep/image,/deep/.easy-loadimage,/deep/uni-image {
			width: 122rpx;
			height: 122rpx;
		}
		&.img-box1 {
			/deep/image,/deep/.easy-loadimage,/deep/uni-image{
				border-radius: 16rpx;
			}
		}
	}
	.name {
		font-size: 30rpx;
		color: #333333;
		font-weight: bold;
		line-height: 32rpx;
	}
	.price {
		display: block;
		font-size: 30rpx;
		font-weight: bold;
		margin-top: 8rpx;
		color: #e93323;
		text {
			font-size: 20rpx;
		}
	}
	.gocom_btn {
		display: flex;
		width: 110rpx;
		height: 38rpx;
		align-items: center;
		justify-content: center;
		margin-top: 6rpx;
		color: #fff;
		font-size: 22rpx;
		font-weight: bold;
		border-radius: 110rpx;
		text {
			font-weight: normal;
			font-size: 16rpx;
		}
	}
	&:nth-child(1) {
		/* #ifdef MP  */
		height: 336rpx;
		/* #endif */
		/* #ifdef H5 || APP-PLUS */
		height: 378rpx;
		/* #endif */
		padding: 20rpx 20rpx 28rpx;
		float: left;
		.img-box {
			width: 210rpx;
			height: 210rpx;
			right: 18rpx;
			bottom: 18rpx;
			/deep/image,/deep/.easy-loadimage,/deep/uni-image {
				width: 210rpx;
				height: 210rpx;
			}	
		}
		.gocom_btn {
			background: linear-gradient(90deg, #fd5d48 0%, #f63724 100%);
		}
	}
	&:nth-child(2),
	&:nth-child(3) {
		float: right;
		padding: 20rpx 18rpx;
		.name {
			width: 148rpx;
		}
		.img-box {
			right: 14rpx;
			bottom: 14rpx;
			/deep/image,/deep/.easy-loadimage,/deep/uni-image{
				width: 122rpx;
				height: 122rpx;
			}
		}	
	}
	&:nth-child(2) {
		.gocom_btn {
			background: linear-gradient(90deg, #fdca1a 0%, #f7b21f 100%);
		}
	}
	&:nth-child(3) {
		margin-top: 20rpx;
		.img-box {
			right: 14rpx;
			bottom: 14rpx;
		}
		.gocom_btn {
			background: linear-gradient(90deg, #ffb052 0%, #fe961a 100%);
		}
	}
}
.spike-wrapper {
	width: 100%;
	&.wrapper2{
		overflow: hidden;
	}
	.spike-item {
		display: inline-block;
		width: 222rpx;
		margin: 0 20rpx 20rpx 0;
		&.presell-item {
			width: 210rpx;
			.img-box {
				height: 210rpx;
				/deep/image,/deep/.easy-loadimage,uni-image{
					height: 210rpx;
				}
			}
			.name {
				margin-top: 8rpx;
				color: #282828;
			}
		}
		&.assist-item {
			box-shadow: 0px 2px 20px 0px rgba(0, 0, 0, 0.08);
		}
		.img-box {
			position: relative;
			height: 222rpx;
			/deep/image,/deep/.easy-loadimage,uni-image {
				width: 100%;
				height: 222rpx;
				border-radius: 16rpx;
			}		
		}
		/deep/.img-box0 image,/deep/.img-box0 .easy-loadimage,/deep/.img-box0 uni-image  {
			border-radius: 0;	
		}
		.info {
			margin-top: 10rpx;
			.name {
				font-size: 26rpx;
			}
			.price-box {
				display: flex;
				align-items: baseline;
				justify-content: start;
				margin-top: 8rpx;
				&.presell-price {
					display: block;
					.old-price {
						display: block;
						margin: 6rpx 0 0 0;
					}
				}
				.price {
					display: flex;
					color: $theme-color;
					font-size: 28rpx;
					font-weight: bold;
					text {
						font-size: 22rpx;
					}
				}
				.old-price {
					display: flex;
					margin-left: 10rpx;
					color: #aaaaaa;
					text-decoration: line-through;
					font-size: 20rpx;
					text {
						font-size: 18rpx;
					}
				}
			}
		}	
	}
	&.wrapper1{
		.spike-item{
			width: 210rpx;	
		}
	}
}