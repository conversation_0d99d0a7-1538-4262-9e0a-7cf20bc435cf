<template>
  <view class="card-pack">
    <view class="card-item" v-for="(item, index) in cardList" :key="index">
      <view class="wrap">
        <view class="card-header">
          <view class="card-title">【{{ item.product_name }}】</view>
          <view class="card-face">面值 ￥{{ item.total_price }}</view>
          <view class="card-consume">
            <text>{{ item.jicifangshi }}</text>
            <text v-if="item.jicifangshi === '整体计次'">
              总次数{{ item.num_total }}次，消费次数 {{ item.num_total }}次
            </text>
            <text v-if="item.jicifangshi === '单项计次' && item.taocan && item.taocan.length > 0">
              总次数 {{ item.taocan.reduce((sum, sub) => sum + (sub.num_total || 0), 0) }}次
            </text>
          </view>
        </view>
        <!-- 套餐项目列表 -->
        <view class="taocan-list" v-if="item.taocan && item.taocan.length > 0">
          <view class="taocan-item" v-for="(subItem, subIndex) in item.taocan" :key="subIndex">
            <view class="taocan-name">{{ subItem.store_name }}</view>
            <view class="taocan-times" v-if="item.jicifangshi === '单项计次'">
              总{{ subItem.num_total }}次，剩余{{ subItem.num_total - subItem.num_use }}次
            </view>
          </view>
        </view>
      </view>
      <view class="footer">
        <view class="card-info">
          <view class="card-usage">
            <view class="usage-btn" @click="showUsageRecord(item)">
              核销码
              <text
                class="iconfont"
                :class="item.show ? 'icon-ic_uparrow' : 'icon-ic_downarrow'"
              ></text>
            </view>
            <view class="card-remain" v-if="item.jicifangshi === '整体计次'">
              剩余{{ item.num_total - item.num_use }}次
            </view>
            <view class="card-remain" v-if="item.jicifangshi === '无限次'">无限次</view>
          </view>
          <view class="card-expire">有效期至 {{ item.youxiaoqi_end_day.split(' ')[0] }}</view>
        </view>
        <view class="card-code-area" v-if="item.show">
          <view class="card-image">
            <zb-code
              :val="item.verify_code"
              :size="200"
              :background="'#fff'"
              :foreground="'#000'"
              :icon="iconUrl"
              :iconSize="40"
              :loadMake="true"
              @result="onQrResult"
            />
          </view>
          <view class="card-code">{{ item.order_product_id }}</view>
        </view>
      </view>
    </view>
    <view v-if="!cardList.length" class="empty-wrap">
      <image
        src="https://test.mall.yuliqingxiang.cn/static/images/no_thing.png"
        class="empty-img"
        mode="aspectFit"
      />
      <view class="empty-text">暂无卡包</view>
    </view>
  </view>
</template>

<script>
import { getMyCardList } from '@/api/hairdressing'
import zbCode from '@/components/zb-code/zb-code.vue'

export default {
  name: 'CouponCenter',
  components: { zbCode },
  data() {
    return {
      qrContent: '', // 二维码内容
      iconUrl: '', // 可选，二维码中间的图标
      cardList: [],
      page: 1,
      limit: 10,
      loading: false,
      finished: false,
    }
  },
  onLoad() {
    this.loadCardList()
  },
  // 监听页面滚动到底部
  onReachBottom() {
    if (!this.finished && !this.loading) {
      this.page++
      this.loadCardList()
    }
  },
  methods: {
    async loadCardList() {
      if (this.loading || this.finished) return
      this.loading = true
      try {
        const res = await getMyCardList({ page: this.page, limit: this.limit })
        const list = res.data || []
        list.forEach((item) => {
          item.show = false
        })
        if (list.length < this.limit) this.finished = true
        this.cardList = this.page === 1 ? list : this.cardList.concat(list)
      } finally {
        this.loading = false
      }
    },

    showUsageRecord(item) {
      item.show = !item.show
    },
    onQrResult(res) {
      console.log('二维码图片地址:', res)
    },
  },
}
</script>

<style lang="scss" scoped>
.card-pack {
  background: #fff;
  min-height: 100vh;
  padding: 30rpx;
}
.card-item {
  background: #ffefd4;
  border-radius: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}
.wrap {
  background: linear-gradient(0deg, #fffaf2 0%, #fff0d7 100%);
  border-radius: 30rpx;
  padding: 30rpx;
}
.card-header {
  .card-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
  }
  .card-face {
    font-size: 24rpx;
    color: #333333;
    margin-top: 10rpx;
  }
  .card-consume {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10rpx;

    text {
      font-size: 26rpx;
      color: #c9a063;
    }
  }
}
.taocan-list {
  margin-top: 20rpx;
  padding: 20rpx;
  background: rgba(255, 255, 255, 1);
  border-radius: 16rpx;
}

.taocan-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .taocan-name {
    font-size: 24rpx;
    color: #666;
  }

  .taocan-times {
    font-size: 24rpx;
    color: #c9a063;
  }
}
.card-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
  .usage-btn {
    background: none;
    color: #c9a063;
    font-size: 22rpx;
    display: flex;
    align-items: center;

    .iconfont {
      font-size: 24rpx;
      margin-left: 10rpx;
    }
  }
  .card-remain {
    color: #c9a063;
    font-size: 22rpx;
    margin-left: 20rpx;
  }
  .card-expire {
    color: #666;
    font-size: 22rpx;
  }
}
.card-code-area {
  margin-top: 20rpx;
  border-top: 1rpx dashed #e2b47b;
  padding-top: 20rpx;
  text-align: center;
  .card-code-label {
    color: #999;
    font-size: 22rpx;
  }
  .card-code {
    font-size: 28rpx;
    color: #999;
    margin-top: 10rpx;
  }
}
.footer {
  padding: 10rpx 30rpx 20rpx;
}
.empty-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300rpx;
  .empty-img {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 20rpx;
  }
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
