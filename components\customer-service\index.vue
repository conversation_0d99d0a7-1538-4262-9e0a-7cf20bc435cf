<template>
  <!-- 可拖拽客服悬浮按钮 -->
  <movable-area class="movable-area">
    <movable-view
      class="movable-view"
      direction="all"
      :x="currentX"
      :y="currentY"
      damping="10"
      @change="onPositionChange"
      @touchend="onTouchEnd"
    >
      <view class="customer-service-btn" @click="openCustomerService">
        <text class="iconfont icon-ic_customerservice"></text>
        <text class="contact-btn">客服</text>
      </view>
    </movable-view>
  </movable-area>
</template>

<script>
export default {
  // name: 'CustomerService',
  props: {
    // 是否显示客服按钮
    visible: {
      type: Boolean,
      default: true,
    },
    // 初始位置配置
    initialPosition: {
      type: Object,
      default: () => ({
        right: 20, // 距离右边距离
        bottom: 300, // 距离底部距离
      }),
    },
  },
  data() {
    return {
      // 屏幕尺寸
      screenWidth: 375,
      screenHeight: 667,
      // 当前位置
      currentX: 300,
      currentY: 400,
      // 临时拖拽位置
      tempX: 300,
      tempY: 400,
      // 默认停靠位置
      defaultX: 300,
      defaultY: 400,
      // 按钮尺寸
      buttonSize: 34,
    }
  },
  computed: {
    // 是否应该显示组件
    shouldShow() {
      return this.visible
    },
  },
  mounted() {
    this.initPosition()
  },
  methods: {
    /**
     * 初始化位置
     */
    initPosition() {
      uni.getSystemInfo({
        success: (res) => {
          this.screenWidth = res.windowWidth
          this.screenHeight = res.windowHeight

          // 根据配置计算初始位置
          const { right, bottom } = this.initialPosition
          this.defaultX = this.screenWidth - right - this.buttonSize
          this.defaultY = this.screenHeight - bottom - this.buttonSize

          // 设置当前位置
          this.currentX = this.defaultX
          this.currentY = this.defaultY
          this.tempX = this.defaultX
          this.tempY = this.defaultY
        },
        fail: (err) => {
          console.warn('获取系统信息失败:', err)
          // 使用默认值
          this.setDefaultPosition()
        },
      })
    },

    /**
     * 设置默认位置
     */
    setDefaultPosition() {
      this.defaultX = this.screenWidth - 70
      this.defaultY = this.screenHeight - 150
      this.currentX = this.defaultX
      this.currentY = this.defaultY
    },

    /**
     * 拖拽位置变化处理
     */
    onPositionChange(e) {
      if (e.detail.source === 'touch') {
        this.tempX = e.detail.x
        this.tempY = e.detail.y
      }
    },

    /**
     * 拖拽结束处理 - 自动吸边
     */
    onTouchEnd() {
      this.currentX = this.tempX
      this.currentY = this.tempY

      // 延迟执行吸边动画
      setTimeout(() => {
        this.snapToEdge()
      }, 100)
    },

    /**
     * 自动吸边逻辑
     */
    snapToEdge() {
      const centerX = this.screenWidth / 2
      const safeAreaTop = 44 // 安全区域顶部
      const safeAreaBottom = 34 // 安全区域底部

      // 判断靠近哪一边
      if (this.currentX < centerX) {
        // 靠近左边
        this.currentX = 10
      } else {
        // 靠近右边
        this.currentX = this.screenWidth - this.buttonSize - 10
      }

      // 限制Y轴范围
      if (this.currentY < safeAreaTop) {
        this.currentY = safeAreaTop
      } else if (this.currentY > this.screenHeight - this.buttonSize - safeAreaBottom) {
        this.currentY = this.screenHeight - this.buttonSize - safeAreaBottom
      }
    },

    /**
     * 打开客服
     */
    openCustomerService() {
      this.$util.jumpKefu()
    },
  },
}
</script>

<style lang="scss" scoped>
.movable-area {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.movable-view {
  pointer-events: auto;
  width: 68rpx;
  height: 68rpx;

  transition: all 0.3s ease-out;
}

.customer-service-btn {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, #2f2c24, #755e18);
  box-shadow: 0rpx 8rpx 16rpx 0rpx rgba(0, 0, 0, 0.28);
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  &:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

.iconfont {
  font-size: 24rpx;
  color: #fff2de;
}

.badge {
  position: absolute;
  right: -5rpx;
  top: -5rpx;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);

  // 动画效果
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.contact-btn {
  font-weight: 300;
  font-size: 18rpx;
  color: #ffffff;
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .movable-view {
    width: 90rpx;
    height: 90rpx;
  }

  .badge {
    min-width: 28rpx;
    height: 28rpx;
    font-size: 18rpx;
    right: -3rpx;
    top: -3rpx;
  }
}

// 暗黑模式适配
@media (prefers-color-scheme: dark) {
  .customer-service-btn {
    background-color: #2c2c2c;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .customer-service-btn {
    border: 2rpx solid #000;
  }

  .badge {
    border: 3rpx solid #fff;
  }
}
</style>
