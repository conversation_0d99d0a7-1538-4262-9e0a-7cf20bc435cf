<template>
  <view class="hairstylist-page">
    <view class="store-detail__hairstylist card">
      <view class="store-detail__hairstylist-list">
        <view
          class="store-detail__hairstylist-item"
          v-for="stylist in hairstylists"
          :key="stylist.service_id"
          @click="toDetails(stylist)"
        >
          <image
            class="store-detail__hairstylist-avatar"
            :src="stylist.avatar"
            mode="aspectFill"
          ></image>
          <view class="store-detail__hairstylist-info">
            <view class="store-detail__hairstylist-info-top">
              <view class="store-detail__hairstylist-name">{{ stylist.nickname }}</view>
              <RoleTag :role="stylist.level_name" />
              <view class="store-detail__hairstylist-rate">满意值 {{ stylist.manyizhi }}</view>
            </view>
            <view class="store-detail__hairstylist-actions">
              <view
                class="store-detail__hairstylist-action"
                @click.stop="openSchedulingPopup(stylist)"
              >
                <image
                  src="/static/images/icon-paiban.png"
                  class="store-detail__hairstylist-action-icon"
                  mode="widthFix"
                />
                <text>排班</text>
              </view>
              <view class="store-detail__hairstylist-action">
                <image
                  src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/ed83a202508022111037790.png"
                  class="store-detail__hairstylist-action-icon"
                  mode="widthFix"
                />
                <text>作品</text>
              </view>
            </view>
          </view>
          <button class="store-detail__appointment-btn" @click.stop="handleAppointment(stylist)">
            去预约
          </button>
        </view>
      </view>
    </view>
    <!-- 排班 -->
    <Scheduling ref="schedulingRef" />
  </view>
</template>

<script>
import RoleTag from '@/components/roleTags/roleTags.vue'
import { getHairstylist } from '@/api/hairdressing.js'
import Scheduling from '@/components/scheduling/scheduling'
import storeInfoMixin from '@/mixins/storeInfo.js'

export default {
  mixins: [storeInfoMixin],
  components: { RoleTag, Scheduling },
  data() {
    return {
      hairstylists: [],
      mer_id: '',
    }
  },
  onLoad(options) {
    if (options.mer_id) {
      this.mer_id = options.mer_id
      this.loadStylist()
    }
  },
  methods: {
    handleAppointment(v) {
      // 预约逻辑
      uni.navigateTo({
        url: `/pages/hairdressing/appointment/index?info=${JSON.stringify(
          this.storeInfo,
        )}&service_id=${v.service_id}`,
      })
    },
    //加载发型师
    loadStylist() {
      getHairstylist(this.mer_id, { page: 1, limit: 100 }).then((res) => {
        this.hairstylists = res.data.list
      })
    },
    openSchedulingPopup(stylist) {
      this.$refs.schedulingRef.open(stylist)
    },
    //发型师详情
    toDetails(v) {
      uni.navigateTo({ url: '/pages/hairdressing/hairstylist/details?info=' + JSON.stringify(v) })
    },
  },
}
</script>

<style lang="scss" scoped>
.hairstylist-page {
  padding: 30rpx;
}
.store-detail__hairstylist-list {
  padding: 40rpx 24rpx;
  background-color: white;
  border-radius: 30rpx;
  .store-detail__hairstylist-item {
    display: flex;
    align-items: center;
    padding: 20rpx 0 50rpx;
    border-bottom: 1rpx solid #f2f3f7;
    &:last-child {
      border-bottom: none;
    }
    .store-detail__hairstylist-avatar {
      width: 98rpx;
      height: 98rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      flex-shrink: 0;
    }
    .store-detail__hairstylist-info {
      flex: 1;
      .store-detail__hairstylist-info-top {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
      }

      .store-detail__hairstylist-name {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
      }
      .store-detail__hairstylist-rate {
        width: 98rpx;
        height: 28rpx;
        background: linear-gradient(-34deg, #ffffff, #f8eedc, #c9a063);
        border-radius: 6rpx;
        font-size: 18rpx;
        color: #a27630;
        margin: 10rpx 0;
        text-align: center;
        line-height: 28rpx;
      }
      .store-detail__hairstylist-actions {
        display: flex;
        align-items: center;
        gap: 15rpx;
        .store-detail__hairstylist-action {
          display: flex;
          align-items: center;
          background-color: #f0f0f0;
          padding: 5rpx 15rpx;
          border-radius: 6rpx;
          font-size: 22rpx;
          color: #999;
          .store-detail__hairstylist-action-icon {
            width: 28rpx;
            height: 28rpx;
            margin-right: 10rpx;
          }
        }
      }
    }
    .store-detail__appointment-btn {
      background-color: #d5a162;
      color: #fff;
      padding: 14rpx 30rpx;
      border-radius: 30rpx;
      font-size: 24rpx;
      margin-left: 20rpx;
      flex-shrink: 0;
    }
  }
}
</style>
