<template>
  <!-- svip会员模块 -->
	<view class="">
		<view class="card-section">
			<view class="header-card">
				<view class="acea-row row-middle">
					<image class="image" :src="userInfo.avatar ? userInfo.avatar : '/static/images/f.png'"></image>
					<view class="text">
						<view class="name">{{userInfo.nickname || ''}}</view>
						<view>开通享六大特权，省钱又省心</view>
					</view>
				</view>
			</view>
			<view class="right-section">
				<view class="section-hd acea-row row-center-wrapper">
					<view class="title acea-row row-center row-bottom">开通会员尊享权</view>
				</view>
				<view class="section-bd acea-row">
					<view v-for="item in memberRights" :key="item.id" class="item" @click="goUrl(item.link)">
						<image class="pic" :src="item.pic"></image>
						<view class="text">
							<view class="name line1">{{item.name}}</view>
							<view class="text_info line1">{{item.info}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="type-section" id="card">
			<view class="title_bd">
				<view class="bold">会员套餐</view>
			</view>
			<scroll-view class="scroll" scroll-x="true">
				<view v-for="(item,index) in memberType" :key="item.group_data_id" class="item" :class="{on: index === type}"
					@click="checkType(item,index)">
					<view class="title line1">{{item.value && item.value.svip_name}}</view>
					<view class="new">
						<priceFormat :price="item.value.price | moneyFormat" weight intSize="50" floatSize="50" labelSize="34"></priceFormat>
					</view>
					<view class="old">¥{{item.value.cost_price | moneyFormat}}</view>
				</view>
			</scroll-view>
			<view class="buy" @click="pay">{{svip_type == 1 ? '立即体验' : '立即开通'}}</view>
			<view v-if="memberExplain" class="agree">
				<navigator class="link" url="/pages/annex/vip_clause/index" hover-class="none">购买即视为同意<text
						class="mark">《会员用户协议》</text></navigator>
			</view>	
		</view>
		<view v-if="goodsList.length" class="goods-section">
			<view class="section-hd">会员专享价 <text>会员购买享超低价</text></view>
			<view class="section-bd acea-row">
				<view v-for="item in goodsList" :key="item.product_id" class="item" @click="goDetail(item.product_id)">
          <easy-loadimage class="image" mode="widthFix" :image-src="item.image"></easy-loadimage>
					<view class="name">{{item.store_name}}</view>
					<view class="svip-price">
						<priceFormat :price="item.is_svip_price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
						<image :src="`${domain}/static/images/svip.png`"></image>
					</view>
					<view class="shop-price">商城价：¥{{item.price}}</view>
				</view>
			</view>
		</view>
		<payment :payMode="payMode" :pay_close="pay_close" :is-call="true" @onChangeFun="onChangeFun"
			:order_id="pay_order_id" :totalPrice="totalPrice"></payment>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	let app = getApp();
	let sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px';
	import payment from '@/components/payment';
  import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue';
	import { mapGetters } from "vuex";
	import { memberCard, memberEquity, memberCardCreate, groomList } from '@/api/user.js';
	import { orderOfflinePayType } from '@/api/order.js';
	import { toLogin } from '@/libs/login.js';
	import { openPaySubscribe } from '@/utils/SubscribeMessage.js';
	import dayjs from '@/plugin/dayjs/dayjs.min.js';
	import { HTTP_REQUEST_URL } from '@/config/app';
	export default {
		components: {
			payment,
      easyLoadimage
		},
		filters: {
			dateFormat: function(value) {
				return dayjs(value * 1000).format('YYYY-MM-DD');
			},
			moneyFormat: function(value) {
				return parseFloat(value);
			}
		},
		data() {
			return {
				sysHeight: sysHeight,
				domain: HTTP_REQUEST_URL,
				memberType: [],
				userInfo: {},
				memberRights: [],
				memberExplain: [],
				memberCoupons: [],
				isGetFree: null,
				account: '',
				password: '',
				goodsList: [],
				pay_order_id: '',
				payMode: [{
					name: '微信支付',
					icon: 'icon-a-ic_wechatpay',
					// #ifdef H5
					value: this.$wechat.isWeixin() ? 'weixin' : 'h5',
					// #endif
					// #ifdef MP
					value: 'routine',
					// #endif
					// #ifdef APP-PLUS
					value: 'weixin',
					// #endif
					title: '微信快捷支付',
					payStatus: app.globalData.pay_weixin_open
					}
					// #ifdef H5 ||APP-PLUS
					,
					{
					name: '支付宝支付',
					icon: 'icon-a-ic_alipay',
					// #ifdef H5 || APP-PLUS
					value: 'alipay',
					// #endif
					// #ifdef MP
					value: 'alipayQr',
					// #endif
					title: '支付宝支付',
					payStatus: app.globalData.alipay_open
					}
					// #endif
				],
				pay_close: false,
				totalPrice: '0',
				page: 1,
				limit: 15,
				finished: false,
				loading: false,
				memberEndTime: '',
				// #ifdef H5
				isWeixin: this.$wechat.isWeixin(),
				// #endif
				type: 0,
				svipDef: null,
				svip_type: 0
			}
		},
		watch: {},
		computed: mapGetters(['isLogin']),
		onLoad() {
			if (this.isLogin) {
				this.memberEquity();
				this.getMemberCard();
				this.groomList();
			} else {
				toLogin()
			}
		},
		onShow(){
			uni.removeStorageSync('form_type_cart');
		},
		onReachBottom() {
			this.groomList();
		},
		methods: {
			pay() {
				if (this.totalPrice == 0) {
					this.createMemberCard('weixin');
				} else {
					this.pay_close = true;
				}
			},
			payClose: function() {
				this.pay_close = false;
			},
			goDetail(id) {
				uni.navigateTo({
					url: `/pages/goods_details/index?id=${id}`
				});
			},
			goUrl(url){
				if(url.indexOf("http") != -1){
					// #ifdef H5
					location.href = url
					// #endif
				}else{
					if(['/pages/goods_cate/goods_cate','/pages/order_addcart/order_addcart','/pages/user/index','/pages/plant_grass/index'].indexOf(url) == -1){
						uni.navigateTo({
							url:url
						})	
					}else{
						uni.switchTab({
							url:url
						})
					}
				}
			},
			// 付费会员权益
			memberEquity() {
				memberEquity().then(res => {
					this.memberRights = res.data.interests;
					this.userInfo = res.data.user;
					if(res.data.user.is_svip > 0){
						uni.navigateTo({
							url: '/pages/annex/vip_center/index'
						});
					}
				}).catch(err => {
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
			},
			// 付费会员数据
			getMemberCard() {
				uni.showLoading({
					title: '正在加载…'
				});
				memberCard().then(res => {
					uni.hideLoading();
					this.memberType = res.data.list;
					this.svipDef = res.data.def;
					this.svip_type = res.data.def.svip_type;
					this.totalPrice = res.data.def.price.toString();
					this.pay_order_id = res.data.def.group_data_id.toString();
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
			},
			groomList() {
				if (this.finished || this.loading) {
					return;
				}
				this.loading = true
				groomList({
					page: this.page,
					limit: this.limit
				}).then(res => {
					this.goodsList = this.goodsList.concat(res.data.list);
					this.finished = res.data.list.length < this.limit;
					this.loading = false;
					this.page += 1;
				}).catch(err => {
					
				});
			},
			checkType(svip,index) {
				this.type = index;
				this.svipDef = svip.value;
				this.svip_type = svip.value.svip_type;
				this.pay_order_id = svip.group_data_id;
				this.totalPrice = svip.value.price.toString();
			},
			payCheck: function(type) {
				this.createMemberCard(type);
			},
			// 立即购买
			createMemberCard(type) {
				uni.showLoading({
					title: '正在加载…'
				});
				let query = {
					pay_type: type,
					// #ifdef H5
					return_url: location.port ? location.protocol + '//' + location.hostname + ':' + location.port +
						'/pages/annex/vip_paid/index' : location.protocol + '//' + location.hostname +
						'/pages/annex/vip_paid/index'
					// #endif
				};
				let group_id = this.pay_order_id
				// #ifdef MP
				openPaySubscribe().then(() => {
					memberCardCreate(group_id,query).then(res => {
						if (parseFloat(this.totalPrice) > 0) {
							this.callPay(res);
						} else {
							uni.hideLoading();
							return this.$util.Tips({
								title: '开通成功',
							}, () => {
								uni.navigateTo({
									url: '/pages/annex/vip_center/index'
								})
							});
						}
					}).catch(err => {
						uni.hideLoading();
						uni.showToast({
							title: err,
							icon: 'none'
						});
					});
				});
				// #endif
				// #ifndef MP
				memberCardCreate(group_id,query).then(res => {
					if (parseFloat(this.totalPrice) > 0) {
						this.callPay(res);
					} else {
						uni.hideLoading();
						return this.$util.Tips({
							title: '开通成功',
						}, () => {
							uni.navigateTo({
								url: '/pages/annex/vip_center/index'
							})
						});
					}
				}).catch(err => {
					uni.hideLoading();
					uni.showToast({
						title: err,
						icon: 'none'
					});
				});
				// #endif
			},
			// 调用支付
			callPay(res) {
				let that = this;
				let status = res.data.status,
					orderId = res.data.result.order_id,
					callback_key = res.data.result.pay_key,
					jsConfig = res.data.result.config,
					goPages = '/pages/annex/vip_center/index'
				switch (status) {
					case 'ORDER_EXIST':
					case 'EXTEND_ORDER':
					case 'PAY_ERROR':
					case 'error':
						uni.hideLoading();
						that.payClose();
						return that.$util.Tips({
							title: res.message
						});
						break;
					case 'success':
						uni.hideLoading();
						that.payClose();
						return that.$util.Tips({
							title: res.message,
							icon: 'success'
						});
						break;
					case 'alipay':
					case "alipayQr":
						uni.hideLoading();
						that.payClose();
						uni.navigateTo({
							url: '/pages/order_pay_back/index?keyCode='+callback_key+'&url='+jsConfig,
						})	
						return;
						break;
						// #ifndef MP
					case "wechat":
					case "weixin":
					case "weixinApp":
						jsConfig.timeStamp = jsConfig.timestamp;
						// #ifndef APP-PLUS
						this.$wechat.pay(jsConfig).then(res => {
							uni.hideLoading();
							that.payClose();
							return that.$util.Tips({
								title: res.message,
								icon: 'success'
							}, {
								tab: 4,
								url: goPages
							});
						}).catch(res => {
							uni.hideLoading();
							that.payClose();
							if (res.errMsg == 'chooseWXPay:cancel') return that.$util.Tips({
								title: '取消支付'
							}, {
								tab: 5,
								url: goPages + '&status=0'
							});
						})
						// #endif
						// #ifdef APP-PLUS
						let mp_pay_name=''
						if(uni.requestOrderPayment){
							mp_pay_name='requestOrderPayment'
						}else{
							mp_pay_name='requestPayment'
						}
						uni[mp_pay_name]({
							provider: 'wxpay',
							orderInfo: jsConfig,
							success: (e) => {
								uni.hideLoading();
								that.payClose();
								let url="/pages/annex/vip_center/index"
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 4,
									url: url
								});
							},
							fail: (e) => {
								uni.hideLoading();
								that.payClose();
								uni.showModal({
									content: "支付失败",
									showCancel: false,
									success: function(res) {}
								})
							},
							complete: () => {
								uni.hideLoading();
							},
						});
						// #endif
						break;
						// #endif
						// #ifdef MP
					case "routine":
						jsConfig.timeStamp = jsConfig.timestamp;
						let mp_pay_name=''
						if(uni.requestOrderPayment){
							mp_pay_name='requestOrderPayment'
						}else{
							mp_pay_name='requestPayment'
						}
						uni[mp_pay_name]({
							...jsConfig,
							success: function(res) {
								uni.hideLoading();
								that.payClose();
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 5,
									url: goPages
								});
							},
							fail: function(e) {
								uni.hideLoading();
								that.payClose();		
								return that.$util.Tips({
									title: '取消支付'
								});
							},
						})
						break;
						// #endif
					case "balance":
						uni.hideLoading();
						that.payClose();
						//余额不足
						return that.$util.Tips({
							title: res.message
						}, {
							tab: 5,
							url: goPages
						});
						break;
						// #ifdef H5
					case 'h5':
						let host = window.location.protocol + "//" + window.location.host;
						let url = `${host}/pages/annex/vip_paid/index`
						let eUrl = encodeURIComponent(url)
						let jsurl = jsConfig.mweb_url || jsConfig.h5_url
						let locations = `${jsurl}&redirect_url=${eUrl}`
						setTimeout(() => {
							location.href = locations;
						}, 100);
						break;
						// #endif
						// #ifdef APP-PLUS
					case 'alipayApp':
						uni.requestPayment({
							provider: 'alipay',
							orderInfo: jsConfig,
							success: (e) => {
								uni.hideLoading();
								that.payClose();
								return that.$util.Tips({
									title: '支付成功',
									icon: 'success'
								}, {
									tab: 5,
									url: goPages
								});
							},
							fail: (e) => {
								uni.hideLoading();
								uni.showModal({
									content: "支付失败",
									showCancel: false,
									success: function(res) {}
								})
							},
							complete: () => {
								uni.hideLoading();
							},
						});
						break;
						// #endif
				}
			},
			onChangeFun: function(e) {
				let opt = e;
				let action = opt.action || null;
				let value = opt.value != undefined ? opt.value : null;
				action && this[action] && this[action](value);
			},
		},
		// 滚动监听
		onPageScroll(e) {
			// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
			uni.$emit('scroll');
		}
	}
</script>

<style lang="scss" scoped>
	.card-section {
		padding: 64rpx 0 50rpx;
		background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAu4AAANPAgMAAAD4RLKmAAAACVBMVEUYFh8jIiovLjUkKlVWAAAgAElEQVR42uxaS3IjOw70UXvFCOI0syVWvWIE65STmSBLVZLsblsuv5kXaLlhWZ9SEkwACVBv2//vv99vCT7BJ/gEn+ATfIJP8Ak+wSf4BJ/gE3yCT/AJPsEn+ASf4BN8gk/wCT7BJ/gEn+ATfIJP8Ak+wSf4BJ/gE3yCT/AJPsEn+ASf4BN8gk/wCT7BJ/gEn+ATfIJP8Ak+wSf4BJ/gE3yCT/AJPsEn+ASf4BN8gk/wCT7BJ/gEn+ATfIJP8Ak+wSf4BJ/gE3yCT/AJPsEn+ASf4BN8gk/wCf5/G7z1H/r5M4Z/uefNbP6OXz8FrP8FCf7Nnjd79Mg/TvnJ+j+B98O9cX7g4n9/8UE+3gff8e/Avb5+/Yzr/8R87c074H2Mzzrix/djPANvTDB2z3nbCf+D//rHz92Bd+/+zpp9Lb0PbMzFdvn27t+ND+Mu21iQ/I7zDx7oP/HTHwn+6Pi+wJv72FeEP7b6DtnqoVZdZ7fT/fHkMe7C7zf4107p/DG34x3xkogFux6/nT/95rx+X6R897i5PeO8n+45dubiHzt+rN82/AhmFSly7Ez4R9f3ue648I/Spm+nemM7Q369cSljPj4eA/yOdqy03rdr7Tu55om26dgSi+T+ju9r50VtrZpx3i+1dsRe+zkU+76ofkiV48+1zeAWWxF0nf2oRI0dqCos689HhXOmmb4uaFf/RPS5n1fQH9MHaeN/kC7O6/RbCLnecJ19J9XtedDHI23eidTQliEOnLTpS+NcZ7e9yvbtSYGyv24DWcvGgYdju/rHjyHYP2D9x+CtrvJr9dbPVoX+dTYQ2qmFFgfWevrcnQ/Au/vDPWwq71/609/vIPxveliUWy8R9xbBLwdMoWnKPdfZYGt91Fl7m9Hj8WfgF8X5WmfJGIfSXZXNxsz6F9lb0RnPynydGeQBvIHdQXTby+pU+qGYuvyPonuVjSruwYBwdPHFiFPSf+ik9q6JPY2f9w3Cpu8+sMvsMZf7KTsuBeOmnHQAD57T3XxC/jfV1W4F/4PyXEox/WPKvMYGpwPt7a++CLOa6yNtQrQIsApRDwk07nXxVGZjq0r532/PynjcJcmbAN6qT/BWnJQg6diRSN555fZgib1OrT+o+vGY8Sn85nKvsDd5U/aFTG1wkwimWSVQLiFEwvRbzyFJ0/fOLyJA3tk8rn6FPQkAv9VYO6iVqVXC806Oi+J0a5eGBlC8kjzaX29BHWzE0KyhX2A9VhF57RC0NxA2WUDPo3yqHgzfdb5rhWN4lFSzuTUD5BuR9V0NzAX2mAqlSc5ltR9EJjw/dKfg5SVKq61ZgW+T7WMpeaVQ64MM6pLeY/teO+TVcJedKtOCXW+NBWgzutiPQuCuZpC8wn60YMrcmHiXlxZrYB6manP/XmtHKTmrzkkbhJMDzq83vl6rCuVLp+J5/oHCZttxWFmWtK/wEffED4Xku6yqv1CXcVbxq0CtRxyeV1IszIDaBDB9VmM7JQBxRVujcFbGHEij32rD870eEoydU9BY/MHr2QZG1gTOIkE6+FNVbg/aQH8q03AL/D6rfePApvvKJ327c1+gXxoFRcrIY/GePKhcQaztNDsbyrAWHCuswhULbLg/vs3ikn5D6Vvb11T3whS4Svz+Jc9LPVTygQtlyi/1vg+gw/mqHrn+YZT7+r/p+1Lm7z12p8YMwXnQ9eC8GFKiuCJJiTUPFy4ilTKad1ZxNy7WC/btOywvpeDT/tvOmnFqCoUexWaGLrINKxvTZMGaTZviz8bEhdfDq3xv7b1/G91tl3yrNK22hwUTruqHiQIeL9PzQ1qxzn3yp6fEWusIFo7WIJOtdfxRYfvWXrbkIzhfLFwSGdkXa3yfrvqcGxVhpbZxKPayNWbC+pTGuKgxl8IDLVJBFw1b373wmsXvvs7BUF9KEGWsZnx67pH1v98EAT4dmz+FTg+jXinKh9IsszEZT7rBB+MbbOjfGqdiwaDAeauxFtJEffRkPVVlqYiUJaXvB8Tw+mSIVL81tisDn8CGU+76jluldC+H4GzRebTjCXCUqAPrwfn+7ohYaXEbk4EUwnz3aJT+bQsZhLeXV60a7BoJsWpINBAMz8rgFD94R/SwyC6tP3F6KUyPTGWOzIsMCg8hvBRI0/N4WLHzqmXnYF3OpBI/qJ265/t71jO3gzblHafPcwTtVOFbJZQqiytjW9JH1eFly/qODDaPxeKIrPa9Qh2nNl2DndGYteX5R+TN2U0hMMn0JvoMeoiwQUfudGdvq3a2bq9a0+hZ8drnAYD3nf52GvbNYRnf9eT7Nhr0sSYZK1d4HlnYexxNYznd5gm/ibX+suWsxUMVMHa1o6uJPng+Rmqq8kqND9/6oKsX3UcbbGpYB6AatoJsUzU+0NAMxYE0pUh71fJS87RoDoBDto4xvT/ljk9ixVim2H/eTgcJCHtWrFD+cd4jNRme19hpTr0tJENXlnvNMqfZaKEQ57Sv20ryUapapM/bgdrZ8/BlUUzT84wfrIKQi3R8WT6i7GeG143zM4bzS5a/idkDuwZLRackdYUpAPmOvmiZiMGywLuebo2yDGtocXjpDFOO+8qmvMaFmEw3vISex532mkUNd9xaBJwSeRFmP0xuwt1tP9LT47/epOMawrJyRV085MyPnq+Sx0bWIN2MmKxMz6vahffa9oqVHAtwxZQJseNSAR7eZ970W82a1GqObNMopOljuh8XghO4Ay3ORVFNEbYQHMCOu0w0rK34Q+6C9yp81l6x1KmNSlFpP9zd/XgaVcQkAtYS2vyCSofn6U/xu4QC63G20trgNIf5kT5HPuBdeh5mcACukazNgc/XbfM9D5ZoikqUUW5JuR0r31RArNAcRYqZUPyWBzZK9cIw4mwG7SA8j0tyj3FXnEfGYVJCKWscCHZ1MV+3Fp7XClqMFEWettgUzUl0QSU8H8nnl75vQ89Xn8+22bQUjuY1lS8632HrqmzDnzIPTSgz6YjmX7Z9KeAxz5C7snVnBmH7cDjQWCNfW3Mbg//4JqZ9cR6EIed1pm/cA2Amh6COC5UzN4OdCJ9qetcgF79sceFSVPRHJPt5DD62W14RV3xqZC6vBOcRz7gCN2yGRJkhPT3v4XnuJLaL4pIjxRbnjqbdjBr4VTtmzdkktZlg2lhDhOaHs7WxsuV6kyRx3NryfCmqUgxKpN9emBEKMzF3yCjnceHGXlaxULAv9OFXrfPz6uryT19AGCs6x97BqloBA4kFz0/s8XCPWqaw7RT6XXmf8YqmryvERmRk5v+i+SVLbfmq3een5LratMH+oivXxPHGOg7st1Ggxde0xHLeRpcHmHxbSEfAG5HNjZxnkuEIga5iK8KkjMaTOwIffs1Sk2mniVDZ2qPI1iPtq/w/fLW1ojkrLONQN8SiBtscpsLVKGr4i5DleUQAARc2tV3tq6YOrF+dQgHv/4rt82MVOWVO58l09s3S7kTpUZeUd9rYyxhPRrpuQyeWyCQSvEjwna0fHI5GUOKmq73Ey1GYK6w6QqR7r3wRn/60RSHBhtcYrs9cwU+bTPcpanikF0MlW1+EqHzV7zd+um7waJNL0UNtLKoA2SXlAY48ZO/ENRkbKXQyFUQbauKwPZ1Htp+0lTPewvZHitJUXjWaUFmRuNF3INw0xdnWCavqAsHPig/xI4/SFXRr4RbgERM3Kydu8Dy3mKofpGmdZybIQoUf5yZ/fs5yRAP2eo0+pAWxo/7Nw4HJlBGSIXRBtFwoCfA8Kz5Tujok9WbcBc1f8UiLGSvdRDqxs+VZFTei8ln6BG6igvu0xae2OALs0dlHm9eIss2DP47e8Qk2QgDYQs9Cg2zTuGRmE5G6Uq+wfvKkgj043E5dydzC53WgT1+x5ddAivvVeE74KQu6SzY1fmqNMz3+ako2okabHRRn9yAogWtNTNPMf/qCnEkBaBurThDYyAK6y/WAytE/uYHF8OilkebE3KXU9LXLwrD+jKW0oO5gtdCYo4rrnJfq4M7n7KAwUdKTrgI0v/fDPSjkfMEaAJdsYBaYFyWDGAaADcj8SK2Osc7IYsiBNxazde7N+JSl5OJgxbl1HkmGrm36+sYSyKGbuU9wXFtxEIHc9NVElxPYGTALcF5AccHY5WAFhOELSEmmCAIlzRQjtPr2NJaj6vAJW0W7wtFNFSEUaHBjTMlUvyzOp+o233Qb7pA3hSfghEU/umxhHg//69iC9NaMVYm9sSIQM1czaDlf5yS2fs7qBIoJiyyiOI6vQJCSeyGygMqjE75pSBlEEdbJvL4/70yLca2qGCX16Xn6WdsF6DVowkcqW1p+qNzIrFQ51Pycdb2vx40u1unSf6m5lt3IcR2aT+xNClCteiMC1tdkEwHSqjcRIH3l5TmHdlWlp2e6LzApj52SX7ItkYcPkXLwWlNGm24vhRfohO9Lc8BYDsZ7fvbHCywkEUzCQyE65bcOpQDtif7oNB4/ZY1tMG1KLPbCvf5n5VLilXxUWlrms1EY2zEDgkFd6MJuTAksZSulQ4PyppRWUN5gjnnUOju0yfC6R9bhBUHZb8cdZBjDyeUPSrEMz4KRijwmMpK9aLRtW6Qg6V0xb4rsDTuEtOUGI7Xh5bMHJUj5xfYSS3zqRGQFR4hvYtSPEOPedjhzzpLfp7z0EyVK0EfwQsENiECRuHLyChvbqSEG3bctKK8EIAYjm3BgD5QXwEdQHiGsTs/mgfI7ZP+I8oQIBAaUMT5nMZtWFp8mbxL+L37wC/B4vYlDWk4gwNQtUJ4p1S6l1WlOg/LaoHBy75An91r0Cv4NK/4J5TtVB4ZQ/lKluMBjziPpbPO4OTYwx0yvRuvJDegdzjEbcCbNdspTHIPyI+grmqMkpaTv936p4p9gXrcuekrGNxI2tiPfTY53QAhB4A6Un+SWmKQIr2CDoAkdl53Od5SnNAbNN7qROhenh/rw+5S/63Ej46Yojw1G+Bv9m77DHYgn5kE0aSjq/qaMD/z5JQyDBKJvUJ51QyzJFEBe/KNDA0ctnITfpTyesb9m2ylvQgV1N/1HBh0LR64dA+kN9rQzPdA1dcao699eYE1bUEIkCcofhmsdKhPkYbNxGuAPysOLwC3b35es32ao2YHsd6eqI2ymYEPnmISVe79x7AYh4UMK9GcT9H+8cGxkXRYMpFiaziDxJcGNzEHwoN9RHjk1iYK3CgNp9vXvSjK48BEECBQ4VR0NrC3hQikOTOYYyJx1BPNMtkeXGa6AAcZ3UoQTRpDukbGbVD1NuAbcMEOAuseNNw0VedUwCQEsMShihGX9je2X5UbOsdGb7saCCMwGysNUgWi2iP4ifQI8UVaCRf4okL11jmed8rSaQCXGFQhDjH6UQ3gtqgNvzfhXcAeszJCiZcLWr9Tyy9JkoPkKPgPzZzu32+JjqVow7DeMSQoHRROGReEJvF/fNjBs1zBBDuoWoZdN+pfwXUQmnEvjFcAEgx8iC9TyG6aIWYAZq+DsJn3wc8mRMEKThVQfQc2NTwBXF0dwLrCzRb6XUCrIGHTGiTBjo9M4TFpiSjSixAjFELGGdnSMqTXQMRhToz+Pq8qVAN28RgzyqcYITPBNQaTHsjXVx5OpY7jbYrhs1B2gPLjP3DzahNg80u806aAe3k77K1XfXdsA5wwYyrWm5rE4wxBBV9Phw0jnTJ6ZACyQDrFoPOmVJodjn8uN4TWjaqRhpY41+DlQQU5zqLjCLtE+YbgNU4VJHXSmKIB4LUPjQP5cnFdpRv+dbphR8wzqlya6odsifPiVMHoQghLEHwwKYZg4Nl7/ufQnXKE0MKhvVFri7JD/CkJymhUTHbszD8qPrhcT8VCXYAhnobhQYL4NRtVgLPUH6EnXg24Q505ai2ACFAMovUzGF0wzsGAwYAnhmFSH9yX90SbHis8Ch7dJzUFND/3DsNnic0V5P4Iyp0JC0rBT17RQlQb4L4cNe25dTn2hNqVmKeoMBFHjP+IK8ClpkFvUHJBUjqhITPDVa13vSlCdT6OG5+iLE0lomotpSLao4Bafz7xYUB6jCDF5k6h1ToLfOEloOGyQWwJ/B0WfbWwEOM0oeNFCsxPfigrT4FE+QEygnv45KsLw7CXyHk28BQGo2qVnOmnDV29QIQrewMVlhNvlAPkp5QnxyiHz0phsDa/Y9bzGQVB7VCzgL9UOQonUhLS3MVpinsfkKNEfkqAPxR/oLxstUZW9Hh+N6oUwlyc3pLQ2MYGRWuS5kLym60hJxUg2ca6qXsA7ZjhmxJZD//vLDKdNXq+o00XhISkIV5p6lCRnWqqF5xDdDuq3HvJQSF9JESQGBBcMrxi7gUatKbICd4u1SWgnNwgLI4XxGf0ijhbJbM0WbjK/DhsB0uhnGIfqNvSHY5KNJyFl3CfhYPgs1L5R3NAeljIcvEBHjsA1hfalOTZFBMISAOC0IrBNUPdFiZFN063YKDI88k38OBBJGkYPpOUrVQ5QuMkzozmVV0+aHSUvSghQ1+SmgOqWRwu/hyxoLXJOfAgZZfSTiF/KDbURyAwgyMWBDAzIARkik7OF14vADXnkvsMbMuCYbWacMadVR51r5KvWiO2t3jhq3O6042g8nC3KHPLZ+3YctZjT0ZxUxvcssjmmWTOxNd6hJ2n9/lJT3aqlkT98q9WPUsu+cm/o6Liyr5ZV5/FsrHf37vdbnM96C+9M8S7X237Nie3n3LZUp6yt5MTVfXG3pVZXtfXh1/HDdFxO9WSmdV+Hvgssc+0Ta/arcVz2Gjp/dzbWu3uP+/fzWu+eGB8KrburY58wVPa2od447mTQ9fuL1dQsd+8V93jk/c7VeJ5rO67EcTPUwHpX6279dLbt997esj9RT7LlV73EOQe0Yyi31fOK+/gWtKteOfPAcZQrZnGv/+7ywzGfT/A3WS4UDvbcW66lpjI+17NZr9Opjmr57b9J+f3LhXaGxQe5R8mJVq6C/IfNp4orwQDg8kqnofz4P6r+5XTcZyyfvvaaD//g4Bf/9OU0jf9dLoy7/3SD0Mcplnkr+5Ibtvj3uGAO3LDr4t7bGSn/z1wZQfm5nWGxyXawdBd4K40/bB4WdzxxenHv+5kpP/7hyo8XuShPXxfKS0GZfB9mtPt2fqpXDDVxpYz8n8P8o2/T8imW8qHy3ZeV3+vM2T7e67o8LiXD1SyVe28v1s+xrFsZuUqMAvWvRW4LkgoIlfBLb9fzGhA9ey0qMa+4wcVZ2b123/jw6X5ddH18OOV79u1FYabnr0slLRHC4YwiIy75sBZN/kVgytpJvEoEDGhBYzqtfpzw97hwIAXK4+LbWRqv+dcxC3sdHRCO7mqxf4VZKPt+Ft8m3BiVU6792rt0xx/+6eMWb/xZYBOUHwIH0YHY/M+UH+riBGxOQvj8ufGAzWz9EfSFlEdKpp8JNpuaLYwHLIzJkUfPmQmVs1GeEnqUahwiaOtzpSD+qTDfInM4d/Cs0C2PkB8H5seZtI0A049y2V8oeoS+0y4PJ4LNva5Ey4L6EoWjjlo+dsq30+jKMKEtVE0l9ssDaqaEuVWePyflaUVF/Xt1g4QJRXoqZXEeyt/7BxYWqiBMf2v6CiHefZ/zCCxbeujKtivzHqDfP+5CtejXiRwz0bzewpWifl5U6lrCQQi/JzCfnv/DiFqKZrdVK7B/G7PmMGCETRPma3r+LzcmbXdFv5ocGP/VvemuYiQO5M4U5dNZQD/JgoXiKl2ZVwjqGvn1ppQKR4H1TNrGFX0CZcGLslLyw+obyCpEGboxJYTscQXQT6Hnc33u717FE/pzhEL04vJ6MKdhmtPA58aFet4Rl5/9e1deCshvK+WWbL0ihuBi+coMOC57nWavi5R/dZ5YPoeF3fZRUigafOhMGXDKO2Oy3E5ZKnoN+ORGRio9He4ATmI0JmWUr23l1D9q7vOS4jpVenWnobrCye6iVWA+t6dHWRtxDw04qOhLxmdmVPTboFw0JrbxoTgrOVsqKJ/V7frEn+xkQtDsvfWP1t5r8RPTUp/1gokLqJj0Tw/8YGRaAGI+7YB62hLQGOzESruJrXaRh5za4SKgyrom1nVt40fgSXre6iIJK4shayLlneStZk5AGJjjAMUfWQWHegflP7wqLCyaT1xJMr5+y13gpErde0cc+Q4jyx18SEdCJKOh8BCu6JRbWEua6VHJgS/eQnnDu0EXAPJ9AkINr7fv/4gKiEmOf2/t1W92Q2v27YW3RZzbVVCw8Yu2IaryK6noM6OoGK067pMd/y1rtgvsQWUnG+Qjw5+nz0Dy+27lU1r9mm1NSS5lNQXfAaFFTgAakIFr/Lemi5EtuMs71crF73QjBSYFCeRYQ0S+Zuud4IQjdKPKW6zUmPInp5/n17uXmJjEdFv1apeCqVvfXt51L6eLJWHHO/g1W3gxEE/Nl6rEiUMdLYI/OVHBW7pr83wMuTgWkXtwI7vXytpNX7FN1HPwxOB3Qblnp++wS4qxVDSpoi4YAh4kjKiKI84FVmOZd45n3sE3ak0//re34dWkmIHWrF7fqe45ycbB876wQ98BDiXEg9IxaYHzRwPmMQEKGLw4fy6uCPwIMwj/7a2/UGC9hLLXWCr/j7az2XEbB4KwHzEXEqBOuZBA9DS5kAB92osHkJ5y66umnOwlmfHOTBLTkW2RrK6u/pGcwJaRo5bKOZeAPkN5nKWQ/NQDY3xTbjMcncv8caEQAe+rx7ESg5ARPPL+Zp4cHCWEnpdx9PgjSFF8jbBE7g/y2pE3FpvDcwqHioXp60ZNBfrZrDCr25lR7V1WEf5y4MfKOlmg3oSb5PPEVDsmciU18GrRMEgP8UJ9A6GvGUdHJiOj9cwkCIefKlXXj7Ry019yGUuPIFf84zZa3iGn6dw4kdmRoagrywkHoPD6srGGc/FMsOooJtj9NJDfi0oRfLHFa5Kg1QLRq3LYDPLaVtzEOkr4vkr0WWyFKLG+YnQmj2zXsQo5spbVqNw9vWhDy5Jg0FnceCZnVhytcQj5avUMqckBfeYmBsOfv2aEvACfW9C+hrWNZ/ht3qn1RnZEqPWZEuOUeAc24Qp4t8hTOWo7FpxAw0iVLxkjuDKpkxvR3tXgvU+53MZLXAHp58O58ryWPjt76eLaoc9t/9wMufMG+NV816sxyuEEXzGGOObqEtSwt7ADjUrXfkgNvQLW8WxVJvukA/Qbmg/ytc+s7SCfvfv+6qBh0jNZ5dNHTVOdx0vcey1M3LF4n9ujC9b2xgo2wbydj/uTL4k77WWapg/dWaKQr6tPW+vqnSE/5nyz6ufPH+s9kF6lhCd24hJNM5KXGhXt+RtfwicziY8yHz718wYMyQ2prm0Lh7ySe20S08702WMfSVMpoHQFf2ytJxg9TVGZAwexZjv0ri0uKSTgnvxK5BO174q/fXPHrN4L3u4WQo3OZa3xV8ySP3mMtnSu81efkpRHabICJ/aIWuoI6FW3WkMW5pGO7gXz/XNj15UIm719GYIjBBLBk/QoMn7e2EllSwkLU1qUkiB8Q+hGPeV6Je8+KitkDWklnjIcbiHH6XzRYQby6KbMaU62DSvMqE+0wx/DOrv5WxifMQZ126qfwgZbZUEWofuddfgaSTnG+u5HpvK2qXLhs02UIiAIeRAezoxKUo6atbVaesYNViCPrwzkzxh1yiT0sK1WpKlSklH8knxSaXpC9zabaC/JZtL7lcLJY3MCZzZyPiSQZSclXq2f6JWT+5fuhNtCXGpsAav9/xGHjZKvdtKD+uY5mZ6KOoJrvTuvvzs36NZ17NQRdVvsPAnQTbSB3P4FxNkN8xqFPWZiJ/VzkG8uPZitLuaPpD2xcKafLHGbvLi9AdlONHBE5eMQAh3MFqAqsUqnpHLl0k7xZjXMtg3IB59MnoXg66OpG2RcyLMZJTbd3/XyFQYF1+LWWYktBPI2HOn0SCH7cln5sZHXE8Nu8GuUNRPNCcySH/NC8NVRwKSrmQ42pa2LHU5kmMRtqId5cFgPH5e/uSMGIa6IRR6mEhfO60mJNQfgxeVlCER0S4xcNH1eHkklvYk4GYbva65K+4Whu2FAzojVz+gwmM0anmlCBXbEXvm8Oz5lBG04+7hEJgXpwtahSfWFkSX2EuAt5If42xdFq2O6r0SVfmDnfmbCAOSpkQaNKzuruG8nBV7IRxHfVp5gNJoBakE6T94Ngdby8XEEHiUO+GzV/aH1zS2BnEYYgKuU1U0z8sq5kM/Xv/YvXY2GApFrIc+HiXNumoXCuAlU/oP8WsNrY7sCRrB41OuFpUULeYo/uk9GbH8j3RpXg3si83ZyKCcEDiOfw1kqm1quARcTNuExhxTU//FHEbT42TpUon69fvdAXhmX/9b4+qiqbZFke1JdSUD2YnJITHmje/Bkojc1/ot8COi4pGe8OL4HeZIrvhSoR26jly4ez4w4u94K2fH7tQMh749nMoNuIxRQ8U+P0OijxssvvDYma0Yt8WCnaqnGZH65O57X9lBi0iXx5tBFdapYKl0TPpY2azq+x7cyCdiOzzMky7gE8hagvkybXhzz0/cjCSvk9JGBOBROWCVk8/Zw01USLziTuzR6o5JdN4wXCZCrXO5v324JcLWjRAUmUqVVsRcfNM+ClUKNBCW/Mg6fi1yMEk1TVVueF/htQ2u52sdB+lBP4ow7YTrWUqNOzErhFPMclXhPe3y/Ea26pbJJwIrDOZVOj4OwrDB5IrQkcPrgSAa2tLhjR1TXzyt6DVCzR5yB9odz2cPBwC0RqW0xp1B7JxQCmCR+mz9v2lpmQx2Q3UQxJUGGg6vZop/YdYV9HxpF2dDiXp3dEFBSd9MDfmsKC5EWKXGv9AgUTQlCeEMqpLi8OswD0Ghp2oAPSSWtA9pkzgD4gZLQp7qdUEMHaOHX9tJYHJs4segrQMhdVTFoByo0DHCHrNVh9VS1V8sOfCb6MLTFWRzlls6jk1zMo7UAACAASURBVHDiPRl5N4kNLaP5hBTAFGGlJ8anhiXILD42inY6yXDaS1+UVmSHo8h1TJcI6YTVumOJTGc+JEaT000Ojec8cs3MG+s+v9+0NtyBx0nmV6KPQAaBA9PDUuxDH9y44MMfHOnVZC9EmxE5qmOIVpDszuK7vK4TI/XqzsR10x+Vr52eUnHli5tWzgPySVzIx5DOD7cPQUDlWTPn6RWmMIefQF7iCFYqJbzg/WPhpHYceBgzGBSh34czWoQSzqe2PSDb/Y3sZRYj30jDtMYM8mKB6ho50P1I3292VDlGg4nk3NpCkm/MHjTlybTz0kqzKbDTu0dqIzeeC7II3MUg4bPgaHGmU9GpD9Md5ez5wbs0t6RQBgPTRN/H3dSOG5fzrQl5EjztBWR8Uq4OLcDZg59gF7kBrt0sfu8fs93NGWuH1b6IXS36xnGiHs6uWDUdP/1t0q6kr4mcCL4cvWwRyNyaQ3VJyuJ8xw8TVtELiGWCbTqlJTNb6gohUZ9OqJteeP+YHFKKQ5SjlpVAEZcFpTAp8GJsTZnGgT7kQz6SmBh51OLEE05o36nuvR6jfrtlp9z6HIqi7dFDpHMvJwr3zjaBkNEsdLay9tnMvPeMNOcCQWqFFpdAGuymxWqpI58NYwvR/MOFypkD+ewGTVodt+SaN/6XO+1KyMsMcL6jlNqscUcWiMba8qgR0iFrB4ci9iUz7x1jNtAgKD5XpBN7dOyJA4AvJ7W0FHdxDty57MF5vd3IJ51IE8tzr/+UTEyY327eomDmxo/a4lo5bSp3gbTlhI8Uv6E3X7mQYMkK7xuNp+WKAOWbTnzRTiecKXSPk3L9lDYejUq5tR67X/iFvHvK19K5v1VM+HmrsTUtlNwMXBO7BXn5b+IFlNqmwTCd18X/9K5RWE+422A7z2hbclI91/K7T4r8uHXGZ/bKErbJo5HXjFoD8/5WDTaO/kK+0FEocWk3m5w4sbeMUrM9t9DjfrDkpnr+64imgGAy23EpSUdc4mu4rc0Zl795B53L7Ctmj9EW8vIJla6/lVQQwBcqft5MLgI4+2OjYABNE/JpskmKkaMAaSELGGbhn0fQC+QNIr8VTHCFxNmzLZuyZzHkacc+SbFqIa+DItf1P7J35L4jYQnkCcD07cF2jmiiWyDCHBNk9OaZbKK4awRkfbVz/m2cvrUAzmc3ogFcfOm2KS7BrJqDu26D9CpHSLyOvJCno3k++aKDiF6cS8WIpBQpmahpCuRTsdoYFZwBHJudI35mQKfH9pcxzkggRdVtC5afgd+n8az4s1ht0ucHHyE1DuRTfvJF7LE5u96OfX7efJ0H5dKpjAY/EIcSpRiZsHV+3k2tZaxrTPKLP48mPqngtCmxBcuvwM81GjMfXJqTT9834UT5dPKWf2vZ4C7R3i/gIOmlDLyQn4HGQn6ygcHTvExczeO03vQB5F3E2ZQX8p7LNVD4VQ4FmnzgcImyHQT+50VMNtzRqpAwLjOkIqnkcgNhQAsl6XCXnkmGswZyvnBbsoq4nsSbbCI2/sfRjXecrneXx3QlL+T1Lmqr7vs59MCcSoHazna5gHDpC9sccRWCdjKWdKHQJZWVNUIOdpOmuYZ5KU4IrMNRg4JY0wftZzVgcOPPYyDAhUVq4+hyFCjghA8mUlGRACZfhufJgVJczewTlpEdZToMVn2deDgbEOdJoooDsKseGjjhWCtfqxY7T1FS3BY1ff3HPZ301xG7xtksiDr8L3vXslu5lQP9E/Nf3cCQAHuVDS8Qf0025wLezaYDdL5yVFU8D+nq2p48BmhA7rSPrSsdUXwUi6SSQJraHJjnfN8H50Ayj1d6XH6rIEW/14K6SBYwzjYPiisSM9SQ6vaBcDQ0y1E/IhUY/zJEqHl4QJCW4zwOsN5fqSm0sIN87JbaNBVQNzSRCPto88L0rb3i6u26rSCh6BifNPGBJB/mDuCMCNhggBkpqMTDXsZElUxW0KOJMEP27TPSV+xpH6xJTXXNw92wIWsRdoxo0UaNEhrDkZ+SGStRQr2lyTnIObw7BJzuy8vNWA+gPXPj+Y28Cr0d0AKD1VFI3HlXUimC55bcgM+cjT5dsVOihn1jXw++ZIQHaJ7SwxNRUiOzvLHE/7YBoiEVOZ/QNJZKXaU2xE1s7LcXck58q4SR0jzgs+FhSKaAbryrK4nD51HUkTW9u6bw0Vy3oPjkuYQJ4KaLmEHzjS2M301y4jcBK0H1ph2YHlmXGcrAZCCjO0FMamywbHfe7s52Pp6UAwk+n+H1HN4c7sb3RZ6tDDf0Idgd3jyfpaRpGoAVKlQxTp9n6Zu378iI+BR1s3FgxlQEQ6Bk35IEqljfcF6phIEsChpUv0LVbswLzA5ESwBIEJmQg5ng2O89XZ30wjiTtootN3k0AJqUTaA70GbT8a/N5KMIhaTYAHdkSZ6XAuuWbH2gW48Nb9Is8keTeyFb4Q1GAJCYvKNehM9vVJw4Hko/JytOQcMfpMVZRaIkFb9uyENb2ce+KV+YQYn+Rm/6gS4fqzGGRnCuxfyCHfjOCKoBBz3goytDMbypeZd7WU9MwAW0Qlm/cc4P3mtMbyS7ZytxXfFEa6ar6khatnXNm/iJl+Zvv8Jb8Af5sd3Im51Nzm7CoAX5LnFDxhLtJZXHyJ+YRujFjUV0GvsKcGNAHlQA7cGjzldGNTCMMHNXmYp2GbEgNJ9BjYzoC7aJkPftlZoHSmInuBCzhUv33Mj5BMR5wDl9HgNlTGghevOqJdJvIjps5d6kwltVFvBtFh4nq0C94JmXhSxx4z7kuzcqB0mDimcj/9V65f2GTg1jDnma/VRGJRLqdu1/XoJOUdZQOeNUH1oI1JKpAUMfaBhY4DNCBWe1TYnwZGXyxPnZFDONsyNqUM7Iz5yMLImjJAbouUBdBEkO4Ku6JnqHPI//00MQAjTr4ResedDegosF8edGeoMOIbQEPwCASqAb78ERP3tUh/UGfl40isZFtwkakz/dCKWcCqAdIyQCLH77XsFHRpCCIvasU4GfNcin5vnAJCFVwlqzHq0yaBWkrIeC6S14lcsy0srDqsu4OfWv3BcVqKV5NSyy9seP99+jgq+wQc3TkEjboeLK5DYcXSBHg2MgGXMoY2gikISglQAYxasWCFRhMNMHWMZNw1p7WDm6g6KQodhrdFJXqo+NAZIZjgadSQwusn30g2Mk8EXmGthGkI8Yz8Hyt4C9KzCpMZeflfqpr1G5ppTTVPqHCQrv9aFIxn7N+qjvBuMS61rZWfElFOFvcpH8nv12pIZNDIJY6oPlk8+bCCwnrSQt0C58rrkrsKBAtC1rZgX/c8IEn5fzc7TRjyvBC2Yn8nIe6Gwagt8weCqwQ/NVBixQ3V/J30iCGCeAKbSDq6AFy2/gOuQ2WdrAQ8gGboWgHHBaqc0qw7BlaakqsVRrD6v2Yt3bhKoCgWZF+u6KAJ9XNWq8/cCPhFt8j7rWZlUoyozWR1NM4Bvk57Rb6ImDevI7n/DO7HmHX9EsaliwNr2HH1aFGuAeYFi7G2slka6gNxvfSMW2RCcK9gpP4Mt6KgbwlN9GKQ5v4oRoC1iJUVhzj8KbsM5myijwRWMvX23kJLkF2cgCCduvxBq6ae9wNqnATH2EpllwEeYmWzE3vpbtGCCEpNfeLyuLy/ZbGcgCKrrPlHeqS+cEDHyLnqWgyaApSUgYEnyJgP67rHcGCf6yp+3KrxxdGk9y6QStArb0csStf2epTF7qo+t0k0pYtkPCiF9eyNysJ0ZVJoyTCK+eDvwQkbXVROJp4kzJg/RhdXFjXRkdlIYqlE+38n89u2IYuwIo6VnIrfHtu1TOLV6nvzDfSYHwsLb5fKX94NaEXsVJSUU6mt6dR8y7N/7kQ9REENPGytqbl7s4OEbwpnarZmZVI3vfrRIjEP4Ptdu3Q/2VFWL1Pa33AOk6v73crUbzrE4EvKGGQ4pGChP10hADKDLrxR8EdFQOofnGSlduikDvfARIUEUnhySqQAAJJJT3ckf7A8WCuh8C9S4D368gH4K7fXnpD66srRmYNtU8hh7pFWpWRNAevjhKXVfqhNtYn43RUasDxwBwcY0exqbmUMbrBpbxOj2dt+dg1Mdl28mb8ExK7PAh5TvZF+m77EikTaWyChW6PF+OxJssoA5eXeO5CsBDxYwqbsuey5mElNLvvBdzI09Wk2T8e9M2cnkswUU3MHCb+vL+bahYOEfsGaMBXxJRHpITXllfVj5obSgaxgRXB8XOcrn3IBrb95FJTfzAiu4rQ2ra44bXcUPRw6KL9iHiCQmyjClOaBypBskmWTjee2TKDzHTuZoKbDR8SCFYXXqBLFMdvRdeWTGXqv1ggTfXEIQWJvFXgLRSZqmzo431oUupS1DfdU4YmcBcvLaiu3sgs8FcfVGaVT9uMDypWkadCo9hxTKIL0t6l002tek2BLQgfBfExsh8NYoiZ+jBTYLpfYbXRMw7QdeqTGJ84ae7uombs5lhPe36Eg+MMuWJUAWvs3jOfZJ+PgcyLClj52PQTJMROms0QbL8T32G7LYq2/QuwbpmWbR3uLLaBNWiKeI3nJ4ZhnliqpjMyCct93KElBdt3CYm3UbKjKFYFKExI6lFr7Ka6p9QN0OXt5C+5ooNnNso502eVHM/DfqEopWUa/SiMrlfpTpP/wJJ66UXtpXbpCK4g8wCE1b/zWP5t0+w6cBTDZ/uzrtVFvJe6GRhQA4n7yTKspcxvFXWJC10jentMp0zIpJu013SKsFGKUm5pu0IXmlUjjw4jBoM/Giu3t1TS7XGow41Eb1mo2QWnsfye3lDcZelHhvm4EDNfMC5lxm8B4WPmO/NlKwISeklF2xa1owaH9c/A1O8N4wVYHppb4JZ5a4SZjSH6hcfO0IauE1rPW+1qenFu+Rgu4RkI9X1i9p6cHyLcSxsVAfjVt3IdTz6maJ7rWCnDCoxoo/gmxWfL6EKa7oNClDSprpKHROcUrRfp0hHfRXjXXC8ebgtp/Z3BSp1WHZUyU7FrXor6T7CcW7r9stL3zzCjoQretUzoWH0V2I9K9R7liP3dc0IMc49/hQjDla3fvx5f0RJCT4v9ZU36SHvVZP4gkV1aDgeK9RiDg81uHdjFmd3t2HJ1MuzRd8qYbMl0VOJfLpalllRUQVeB6dNgEnMDo/nh1R9/uXrD+tfO5jnkxs1e1fxQ0U6Tz5fj1VFalfakmBlKVVM+kGHWqxkaK6FJN1PqwLLidFD3PVXt1IwTSIR7hVc8xGz38pmMWL++Ky5KKAdVdXmORNwBvCEz7w/SHG5WPTSQzf1Jzc4Ubnbmm/IKmdIysGUVjX7qv5RtrpdDAxyP7aaMsc6Dkf/tcXAbLE5vdg+M7IPUun9psp+/JRtk4Guamid+fzxcXNCcUvbx0Krjs9jw+wBksbqZ4rOx+pm+dn396wG0ZcXjT/Yzw9bQzWG9e/r9amSaLo2CauLTWexau+zKJuvrvS8GuZr5TTaIlXdHbDcV8Hd1vA6+Pzpow7+knuFjQR75p6+gsaZqvu8ZgnoI9xNXPGu670FxG1sYS8MObweI6K2Z0TRpWgqrva+4WsFFJ0C1DymHcV3HRzb+qx0SrjeKmrFsedJSsX0eT80MaZmqzXpHUKOCHwwUCzs6GkAFZU6nLcY0Ncos5VItvWeTcJXTn//K2JdxgQnamBkhz5rh4XOl3c4HHsPW1JdGTvczlB7H/VEm5gMw07z7RJ2de5ZyyJ2jGi9Y9hCaPKgklgRZMnPKhmKxfQs5qOGG25zAle9Nf0kXTw5+PCxH7e1NTw/QUDyRPHjC5OR5UTyr1ZdHctz2I09h5kDwP7+XqXRnPBEjhd72M+c1VKR+VajAt+LvObgtkebRYp8VGAemNdBuXk4+oFG/bOWe+fa6bIfZ9ijH59y0IhTHuiPB/cp6hG6Ytd7jJWjPgT5J4U/yON/Vnt/z5cPn7dliPEMnNoBkQ/Epz2teE6i9cPY3JW6e423OATsJxW4kiY/VbvbnmbkU5x+tu/HD7a/7YPb7FJJmD0pLx/cOz4TN/Hk5PaJXR4Z6v/u83/KO/1clfkhJr0fWTvh27LEucO/e/BzFjjfIuwzSn9H+BO28TnH/dBTnyky/5JJ/2G3+We/LuEv4S/hL+Ev4S/hL+Ev4S/hL+Ev4S/hL+Ev4S/hL+Ev4S/hL+Ev4S/hL+Ev4S/hL+Ev4f8fwn/9Sf9A+H//pH++fv3ysj3Bv37KPz+/z/+XnTPMcVQHgnCQmP+2ZO5DJPv/swT3v8qrr9pk9wCTBEubGWIMZOLurq4uG3a3OR1fgM3Unu+9txl/lKppWs+3K2GnJJtNg296ndP9HGL6Izw/YXUdFXbbJiTLdpytucJOCvpTbCOqbLOh5tDv2a4iNZ3bRfWwzVamUwi9bQM2TtzZuOaEZ5AHTTHY5vrZDoww5h2DmV7IMZUqKqwyd7IXDleZTQ/tttlE2aGsPbqF2XH0Xqf6acKNSEcJ25DGk7me8W7dqwfim8nYhklgF8+3jWo7mT6IJQ8wvzGnmmvNZtPw2wHm+7aVPhduNPx8nDuYj/nITJ43yXSEWWtZKZBb7X2bZJPjtSPMd1x/tOH/KbbNCWu22Zogf/Qs5J9TbFu343MscQfhsIowwZY7gmY7kQe1bXlQ/SHo33+T55tGK4oMzx9n0bGO2rn9drR+kKGdOWyvmNKxRZLnvP3mEnVoL6tIlVMj9+KNgnLef8vCvcT8lr3Q2jQBl8ZE3mQl8t035n0dsqRIieW7xf3ZffTuGwgvYJwipcK6iTbLWZmH59tvh6fcYpetaxpoiba1embv3H0D4eiZjiRmAi4rIH5Zs91+614YFoBy3/E8tVZpi+f77TfejlqFlPLyPALBO3ffWOrbmDyxbnOgclS2ICLLnZtvRZPAnhHwWarSDr+I8v6eP8PzbNI2dnjW2Gfw/MGUCc8rYV+eFw3lGTyvmesZns++iTyV50WQ2Z4/7Pkz7gueNW4Q3nyTIvNC69F8E7lW7UCbh3duvnmQTTSvaas9v0nYUKJUecvpJZz7tswCS49J1bk/lLibF+upXjplzXPXVmNnne+ojSrLQivxUDJIV/Z8tMzCyF1bUSIZK2KEbZIm4J6M661yU7/75vJNW4XAgxTGYRsWneR02VbVVatOu21b4fUioIgb83ns8jyzWszLfpzCRt6zPVDz+P3QxEnjx/MyBcs2wFTA1l1bJnwMUimqIZ9FbKOqmzGvj6cptuOubfdKmTrYocyV5wUfeb6oyRXs+/eWbSMADHKI+GAbkacfd+J949Gncsu2A3vviDnF8Ny+z5p743qsK5KchaWFfMP26PY87xp+Ec8XtAJ3GHy/pNGVhGg+crNW06dDQmarBzXLnvcaTj1sIHcIi1O63LCVnJTfWaRhsRXMF9lUwusGF3sF6Xa7liqlnbyJJVm5MeYlGY6BrbiRfygzsHS7Wds9sJg0cVietx0ecIP/NT3PMrIDqnu1ArpCIAUs/+tAl+dRyMywiApFVnHRebiJm1RUrpu0BWkg8LM8RgSaPW87uK2merWBnVZY0pQpwlm/RVuYPBV7XocqMbDncbNvbhbfD8x4PjP8Xvmo0uUObSzzCR+9xFoxC2fp0VSduJepgypQSgBdolOqxJhyKIG/3RZ7HgGj0TFYPE+18rMHJiFbIMEjCgI35p1G/rZvt8BA+xmaaTwnQWaWE8wDJjhSXhewsHPDEvQPQWHq9dWWoQcwMm7mQDuMf+awYAnO9xr3AeMfqEzm6/wNXfrVths2o/Dn5nX47Ae1NJOyzg/PW/jUk+A0A4mY4YFvtd7L4W9nr/wrdDTcLucjia2SfVnlcnke+DhfG0z1vVYujdEBgiAQ2gxIdHh/bCx58OwE96scAVdf4a2G1Kn9a618r7krAGLE7osN69n8fA2e5+6mzpCh2i8I+4Lru7vufK9VBGDFnF2YDvgyUlFFVJ4H6igbSAiLSAXtw59CHrx/fqlFqEtQSsHHY1nNM5HDJEhcmIzIOMMKO1ve8pitVBcCDFyHoR9uC1BmZp1T+B/uPEu2urcg2HkoVAXJYphs4E0KIvtCTVrEm2TNR1tc65tOGtkBEHIUWGXsU7uIemRmTg8Z9ZPtfs9XGHVoS+H+SanIJtlPtjwCZIrJP2WQjSC9EYbiDCU9CwkrVB3NhQkFoZ61mYMmU6tnuLl+qm2eZzAWR0Bu9MRbJ5yqTtYQmDqLtslcAaguvsnZwKE0gMHN+rN/pDU8sACGyW04O9cxlGZtz/17LkDbMGAUZQmCyS0yhVS5BE6NjH5720x41UWyNePHKr2M4GRIPC5ueL74OQRwEjqzj+pqUc8PNa4MYfrulu8n5AB2iDP52mvYTFJJgDYkjt7SwxWYHPd6KzbXeB0GETshjbL/6nvbUYUc+u7AF5zpOhX1iQrLgEhShFkNLZD9V1xju2e0JLbJ1muYBO/tLbogj5Hw1aCEKIQ2AwRP56A/wIMTxhEBU1mrrrHgSWd/jHoC5ZpGvry7DT8VD9VDbNRSDzfGiUlQkqnfRcoAF8AwK+coqxGqGuTkJIgZ5XvbmNCZZHLkYCSyV4ir1w4GLRY/OJHzZS4q9GCGCLh+WM/pl0KIV0DrnW2JPa8T1JDDrpZmcI+xegpbjDNUZQuYuw78yAk/0FJuUWtdsUPpY8ab2z+k09aeQxRXHvMgb82h3fgKk/ZH5KozANZRMA6rzqBUmx9Iq8PImMW8px9f2YNsIpF9agyleORBIiX0vP9EiX/EY3EZSOO6zZkTBwaF9kGm43t/uR+QzxYIdZT78fUvh7r4xwQcr182XXir17z9ZXAJMjCaBptWM8Rv94cs+LPjALURoUiCYULZHy2iYGuj1kalHR6/rPLy1fDFYFaf/eX+FQgj9yL8CzajhPYWsLDn8x/Ij7JmQZGvz0eRixiMNIg7b+2VFr/TryPoIcZyHbngUfjNaesj/jA3kVkeRtlcpc3Q2QaEDJjt0tfW01bO/jEf/V6/X7R8hf9VdXxFJOufo6qww+vh9yHR8lhxGBSbX0bVV4TriwJ+sd/SELFDEZf+VxguJgpmeRaosvvBp3jLuV+PQbmJ2fvo+8B1Wb9+f7Pvp978lfHlr/Ya2uv7zT6dCvsTrl3xwlrZk6F+X+uVt3mN9icOjt2fX+yvfPszErCN/vXN62sYPz4+uvvc/0XMPvHrsc378hx20hfTwHlf/z22mRNWtWrWH8Fm2v/+rg9hNudLnp8ZNnN7ft7B73N7ft4KW/95/p/n/3l+Ns8zR5xxm93zT9mQnjlN1uY1vTy/tslaTwNZoWG5JM3SrsZKVQzk+VT/G9QzRdt4CCQpYxMLrfW5ACRtU7Sp5kUxWMU3+0NG9DQWMWdp17+K1JrjbYpWr2UwpQbPIQXDafBUTmDejds16NJHHoF9Ja+zAAqCQ1m3vWt/LCqDeRhoDe7XkWdeowxUJcbK9aneq+/l8dW+psIORNmYMDAqGBemaG/RBzlmnfVpo5SwS9rrGC+nd5/2FQvmjFN36Mu1yk32HQ1g42K1DmyZi4hSjjxYx6lb9A0RgF9MNk+xTQqpYIeneqEmBaiekRAOxrf7yVyDaxdGuoN5IrI/IxMEJi4wvnTwisZe9y/3PWgoJS+rrajPH7PN6mwIQL3qQYggXbiTGs8b9ENKsre664TdB8rTHmptXTGVzFhNrCPxv9m3XxkSpPkcNVUnHsAmWfQQjmVw0hLlOLmeydRljb/2nb5Q5BZLFmszzFjRNjImXZgyEQ3KjFA8I0LZ3Pqdvpk8ElYjDLPoim1IUDON+mlP7KRgnVHf7IEl1RAV2PrZvsIATta82PvJiEge1iMNpbPWRyS1GXbFcsetPiO7bbhCmj7bv7QBQMg5Tjp1d0tin2O8fQ3Jo0/g+LiXnkYEUr7C6OT/WL8G7peIiFBjkAg7noDXZSEy0CQACmujGvulT3O2xmfFROpy+EP93cMX/Rnbi4ywFUbL47H2gE3Q/HNwVST3mKYTqRUCSiMoXPGhPhiPDaC4+Kc/sDHnxGFoaN/ZmGMJZbxwBB9PHOUESZE+1mc0qrcPDUfVNTmDRYXel7bBFO0tOSjHdi4LKR6TLddkV7Xkv5pBVv1Uv47ww/5OXk8H14WMSI9n8PyzAm2FYX08oyYTCl64gAQYq5r4YcWcD/TRxYkMSFeh1ckUCJd7qbApiPNSNlimxNBvMs3L6oWaVZNT20lhXff+Pu9BegvjNhftNa6BBR+e/zmHGb3c7IEKbVZAEKy6S5Q6iBM/7DEJeG8fleJyQ+Y5KriZWlWT+Z4KS2HVKBWfHNJGoJfKx+y0kyJO2JSCQSN+DzlpeXMfgtQQ8LPG7QOhdkzqq5LyAds8iUldndv01j3ms6F20KW69GkrsPPy1PI/7WabJEeuQlGIYAHKHSkjcva/peEcVPa892Omqt0dbrdLlV/SBS4XlP7RsdnRKZxwsFmJBzTzYJB2knjphGuYCJ+Ca1nY8bu0LB/u7xX1cq+MZbr+wfHTvkqVtHXbZAJQSXvAg+OTUUX+IYD3HnP0oG3Q/zT8bSvcvddopq1NAjRd/KW5fm7cURmgWOgCcI7aR6f17HFwYiFEuRmJqYI8h+OVck1cUP69iHdzXrsba0bnqSvWT4x9+FJNLkAGeeNVmek8uKrCuiXVyvg76N9pcCiI8a77II8i8jEudduo+oFxWMT6PMiu5MeOu7ZM+38fN26DLvHeqVbT7ZQTprpb/mlnNNkq9EnEKRHl9N5WBiXjvabz8F1jNGO7AZZeJqgKGzadfPD/xBA0vMMuH9wOE7VzN8/XiApyk9oaMu2DiEtg8vZIjrDDA+9qsm8b34PxQd4i8CEU81Sy1NiEYsTS1QvbSLOsqvNuSC864R1kKwFIVjTIk45jL8e1DZY217eMyUbOik/MAJPoXpJfR8RJVEaKHeNxyFp8GAAAEopJREFUc+ovkX+2SmfqXXKCAEBSB/klPzH2DqzLxPLnY9jaAIyTl1QGBb/z6I6Iy5zVAcsa9fLJX22cPlZ5QtYF5fnAj8iXyB8z6J/UBg9u+i3jzktQ+qMNRP7YQV9vIBvF/hwql15h2E4YIUPgPseZxPt8IGI7yB/CBjfTDIRCX1Ljun8+xhKjIafAGOSPF3jawuVpmKliNpGZuHdqCEgm65fPZ50PcG4C1DSAjhmwzLZGgxr+eGwPD5KDJgb1/mvTRuZBptvAgW32bJKI/JqqfVMOOFSJmikG+Xa4OkQQ2MCrzS/cv+5vGKPobWRI4EUpt5EJIXwp3YTWEvn67fOBotzooxxSt9gdn48+mkM1gEFFb3j0VRoYuNafj00raRl1mKb5HmocOPWOaylCo+lOv8CJOXVf9hDWbEDEmoyNyL5QGxuN1+f3ZWvbDupnmvT2OvtdXx9TPz+Wzsdj++A26ysi5Q4aTU/ZlGpG12NKrNtePUXZpSSeFIIahUNs1dQ5e0R1X8NXeqEEhXfRTPnyuB8vertVCw/GS+GdRGuSf/fJ73zRxUipLW2WIPSDPjE4pKkPgt9KH+mrE1bflgoY6LXQ4jIlB8HO3Z4vjkmSffOd/LpGnzCzXhgaopiaWWbxccE27Qq2SPBrIZjOQrpcyu9r0t2aciGty5BBaRPCXyM5lNlLJL8yBg51MLRxxWiQy8S5x9MpXq+pdCWonn3hwWzkEzArBmPrVmOaIwji1p/NL/sJzdHQ9y0JBeDvEQUPeiG13VfGuF/PuXFvYggUPT6Suq9pq/+EZctyEo28sPZCaY6UNfdtydRLSzWknZSHN0XgF99a6DXZAKcy4Py65WtkuFlifWkcNK0BPUfwUDut5s6imJ7+sT1UxAGTaOS3LYcOlJbHHdWQZ5afIPWGIKkUehWuvKmhrUTIcwE3jnlA/5Az2geCj18YQ2Kt9frjg8+3SRU5hJys1k9O1MwCfEwWL23TV6in6ZJQYqnc0jK2j4BP2+WZAmx6J0EzItlM7Jzoz22vjXKn4/rz8WWUCexl/tjknGlqrCPiOZ8+0mgq/PwhLkWYKrLJJPE7oAicEYgfwO8/AdtsBHPfmCoZa/f62g6oUWobSLnW5+N+GBlbYMOISpQCDjr4KuLbPKD+wKXt86wJneyy7JWNbnBnCmcE4rYBc0dKU6sQKEHgt+c0IqGBvRF3Jb7i4zENugm0l88/g7ykBsewhKYM/D3DzVZ0aChS+g4gDv/04to8OhnRABYX2FDDF5EFxXSIkCMoWzo+QhNm6KlGxIdj2MxAw92NAZ/NQ2EWGXBsEcy3jzfyQTFY3gRn3jh9L24PGE1dQnxRXFkGaDga0DymwxXvBcT+h8PIi6D/8Ok49Zw9kOCdXQ6VcycFjG1xVl9jLUKSDNszJ0dInPVCnvfm+jqW+JDxutTteWMEbnYZ5rIxjyit127HM5gTtvl0DOt7L+glaMoEvCNYbduEaVQHjWOMQ0GM9mc1ib4t8nQLadkX896+ScfdL2R220sShQWgKyrnQZ7kDgmwuk/HZD+tyKH2oYu1JZNEkbCAyxj3f7mkZNhPf5gniQw/jOsgfztNLAvkHdflB5ZEArhCerC1gKA6yKvF7UncH45xkdv4eW74DofEoLoHnW1MEQgXZkMeEPm+gx0Erv6F/FI0PdyIOdImLt3fAgfW4cnI0g2NHZ9HW+ARTOfDsamJlEUUrenkYVC4tMDO5A+3hO/GyakgOOkBu/1GHsLfZs+eIyZAv7f7p9ONiQ/uFKYJuIE2M9rn9E4+G0PvmzptXjkUXxyJqMBjSOdBReIR5GSuOW+6AYT1L59XewInc7RFjl5z76oekZeNCWLaKYM8ajn14Ss+HMvsKD+c4jqefRkcQIzucpE5/7NrOD1kc5RC1j+Rb8YKNjrLqpfz2CekTvES42NNzOk5GMkN0jVIIl4+GS9r/rjs6kJq2ycQHOk7EnNmmSJ4GNURZSmdEtz5YWoHeWUePTFum26FK82goOt/kE+JDOxsv2HWlCk+GqtW7BrIGoO43TuajY38nIl7M5kciJlv/7r8jSw9X7MqkL+m0jURAcz/IZ/xutGyF0hB8Ijs89FY5MM9btNJr0Mh64CqiTNhiSJOUz0IsOXMvQYd6f7P+CRbDhuZVPasEGThbnihxDEXGjDchVBQgWTRMNWbPhjP3CmfljNxkG48MRV3J+11kKfYs3ohr8i6JwURnXIPUUyBRY1jRPRZd9CsIUKo1DeSdSOKI6f1PVXj7ocgnCo/GI/GDVQJTyEFhUqegai7J3iLvD5+D/IxREfkYgUnsub7msIwreCXBcE9jE/xbRuCha5Z8Ckiyea41f5gfLxm22bRhPbLtObYR7YT+QVpzqzpOiHEaFPB4aFBRpX20DrZZ6AARxGYa22uYA/Umd/eI6C2XglLfDKmHEbgT3fjmsG2uWAa8T2I0udxBhXmVvfLSSCPFL3Rzv9EHtdzrriptbvIhzIYO7AI9B2iDxPaG8W0748FatqHziRMmpX6tkUonkuYUK0ufdtU6tdo51vkD4ORuWD6oVsL/CaI0FYc2LOZmLa0pLCbapcKgfmUfYD3xjoHyAemRmhT1KMI6WynpRX10nSWioaTxRbIu+EtmKkPc9ROca+Q3eSUkhUM7pugUSmZYczx0VlVSaBIqr3HPd8cU2GzEDt3BtGU/WGNZXsByzNbwd3MVCsMl8YQyPgcy0ibBPd8MV18vVtt522khZzDHFLwk9UsKmzjvTe2i0cvBtaZxOELEEspSUCgwW0TLVnIOTKfyTtXjnGW26DTvLKTF1MOV4y/QcSSMbcxVxHcOQrxeNxCMNcg9NZYRVuTadY1MjHNiqYngpfkma4MPyEufiGfQ05pHat7wEroxo0jXrOsjvEhYlpVm/uZahTzy6Y0jTQLszBi6s3xFKH2L9wXHu6wpCupSN01ePJtOVN9nqy6Zv2rTsF163CQVJgl0oTN3SDiDjZmUDHOtoTo6Ls1GWRP8l5vjUlWJ5fYzlkmLzQXtLavsYiuzDLoz5wEFMcZdOZ4kYt7/WFBJSWG0t9yHyBiyJT9OHtoXCDyoUzwlGJK74xrMuJWJ0nBo622waUexiKecGRW+MvL00hRi5ZtckA1h9QS+duy5fLb68geUwyixFmMWuLJgoMswXzXW2OnYDq6RoQoNXxFLIbHb93WmSuzylkMbRvsor5PGO1RcSaAGPOk34J1KeYsbPSc8sKbiJntlT4Fbe7PO+NN/yUkFrDwE5ZdppMl/4c1bOaZpoUMBRWh6bssw0XT+1n2V/dkWBs1aUN7SctH8LD3g5Wd0Ugf5kUJuk15+53xUbzFI/DCOw/N2w1cJ1RjdlLt1pezEG82PwzyHA6v4dY07tN13j70OKvuuOy4iLzn5NiEeZD4VFH13ngJXrzsodAZy9jNvGs89HdM8vwabWPv2Ihf19CVmVMsx9sulxHj73U6KZzpTl5aQ8QR6ZkjUGVcS4T/GkuEbkQNFnSzD/LxQgj/OK/z1+zxHT+3dpVUXTLPr3P1HiazazPcUkMv1LM2MyPGJw92Wzu4GwZx3uO//z7WJZyjW4P2OcYP9cnzsqfozXz9zRo0+z0V4LGMC2FTPKadZOkx1lx7gvZlwjTYVInuiC5T4lRBTshw/I/xwAdUxylu5zZsMM+B2WOdFwjmjcNDI/7dxvfs048XaMw4YX02QQGCq+O8DYLv3XX2KKQt8/L6u11ry5Ebh4EkwANwbyQDe/8zLZpFSrRstycTYL+qgmTCbo8s81mkrGmDrNNf5VDAh9dYnHBLqPuIQIh3Kf4NAyDh56NolJcjbIRpnaEiwzVi/BoZ8cDkBna1LFK+NB9yvJQ8U2DUqqhXcRL3IsfNPqxLchCTCg2j2qybMX3P905QxvJqOHica0YHo0FtPL0IbFRwIBfNsiBmQPbDElEGg3WiGjjuGxZ8kT/zjnCi0F6c8x4oYR/domZmKivdB108cBALkuagcRnmB1IXhjWgFI5oq3sKulBFnFoEBi5TJFDPcH6RY/8jP4tngg00H9TRqx5gvR98yLhW8M6ZAhZ2NNKRXZJzDUxrPtdYvEgafWFk2CEoaJKEJx4pMify9JusgsFPWDua78hmR9BuTz9F6w3KUsUXiyGPaDlXkOP0v1RO/F/rU0m9G2r1P9h3tvO5ULg2jPQix94VbSuCfnqHaBWC/G8xMgwa0kXHh8cXsQXZg1Y0RpbxYgJYX3TfMWjBCMCSnY1UgsXAIRnqGMVHvsmHIgXBbOHziu7nwBcwqYJbfQ4Bi+CMkfQUasaBZqVXBBJmC1LjPckhXzy91JeKHt/qujhkB/Pwr3KmlgwE+C1GrGizgtgkc8+hIHq2NE8ctGH/qLCS5TtolqR5gjgjnEDgQwnIGmOaOtrx8kVQE/kqxwE6Rn+jHh2GxIWxn7C/r3ztOfSqrHoED9CkEZo9wwF3DrYwcMx8ZF5VKU7/eWfITyERPDwrZNjpi6ywIwZmDueP+1uStAgj9ILxW3V9VVdHhz5APJIvI5gO0D1Q9s+rIVngNA6kUIc1KXYMG8ZAlR0x0I/1xhcZP7WIy1wf9xvBQXHgWs78SZQWzYTNGM8/2RGUc82Uc1glAi8/98rIc7AlFRxD1q2mAu9kLUV5TEPTsj4qKGHGDICo+FVVfbatOGAyeDV6nGEyObRH02B567hoePKeqAHp9EHRzZKux7G4ZOP3JEc9heKzslvtKN7pkaT9kkdgsY3cuKVTabl8JBctqiyOWWoxGUVa0CxtOcWoeaJUJEzmGoPMqpi3cjUYqi3op1EjA7os74yIyWGSSzaicPn6/ZHt0qTQQSKliJLXN7OaWPPYPDCtrjObzWc58khFGwiVZXcwl8+oQrZCwsrhncyCOsDtx0rtOICo6otZkGbSQE3BVF5VzpV5JqNsHp9kc/FFJUEUkFxwspMzhAjc3OQ0WFX6dMiFSAhFlCrt2tJv5XfMDhaTXtrKtlRzJvEgI91INnJz/VPTNMkv9m9WPEbW3rxCO8h2Jpd8Rk2GoeV/Womg8nok47CMlwUqK40iobcyFjmkztTw86jNJefXvKunVcpYUr2G5kR2PrRm/V0RiGIlMknF1LXe6at1crcyBgGizZWW4mNQvVLq/LDL0FiStyx5IBlw8pXQ07GlzjorGyBDowDm8hjppm1gjBs5dzyPsqF71anW2mlydYRS+oBq8/GiEO3hRzEbE8/cMJbXW1eizSVmBqwuf2xd/9gMlEV0LKNkXs8o6tmwgmTUAjnSX4ZLt7LK58ttvEy1Lq1fdC/faxvTtdqdbHn6pLXgokd5RhnbyHLfwqw1hPloo8naE0d3UeQgKyVq1czSilcYjJnFM3Zu5bHq1KytY2y3HXLeKpiWnQY5eEhbvUz5fLTZulxjWRN7X2kXjW+pxw/RrqaLfI5kKwagVYpGPdkoPp7JVqcfjJYErD3uJHTNmxe/ynu0248eB7u2buUcisooJutNmVbkck78uqFa4OTAtX65kkn4YraDLt0xtV+6hahk7rHVdviDLJMpuettzq31VR4Am41KOFnBlhVWArJ98ZxSlumspX3QIMM/fidbryurCY6znDadxFi3bqXooXSV6IsT+rXiuKwaO/I4J2/usqJDdTPEvpTfheKWlbzaG23fql53qtL8dDrYZkDZ7tKCo8Xg2Eyj2153eXES7Q+jF7/ouzXZkrHquWrjDRWwwFkty9OLj9i5TucKPvtLXyRD9Vb2OTdIg0lrCVzrTnMuaZMSiFzt2RPK6RJtdHIppAqd6tkNZhrTzkcu8uY/fvWumZNM/LwnXYWp+fSdbbunzd5GpE3apCl+84/nn35d3ntrfN5IdmDeHV3uQ3a0tDwePV+2Wi0uvQmy2Y0vBtrl2QKMrdDsRecxUYLftfKwPLtHxXRZaalFTie60hO3rJ+N8OgWbUVD9ZridQV0KyHo7LTGdjstbH44mYt2Pa2B11k3dlLQrrarbKfPyx7WM4H1xzKxb9bQR2//EgiypeP+YQ/QXb7c/nl9efHbjUXapYTcJojdYna31g8wjWDS/9ptMdtW12/CVrX0lK/0izYuxP5O/rGG9e3RZR3/XZRnP9XfubV9v+WNge3JHex54R+ow39ypf7Kbf/sCpX/BeNFllPT8Hfw317h7x/5u4L1fQN+6cF+5J3vbvnHsJ99/osI1L92zb9yUn1JhQRBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEARBEMRv8R+g7d19aES6MAAAAABJRU5ErkJggg==");
		background-size: cover;
		background-repeat: no-repeat;
		.header-card{
			width: 680rpx;
			height: 200rpx;
			margin: 0 auto;
			background-image: url("data:image/png;base64,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");
			background-repeat: no-repeat;
			background-size: cover;
			padding: 33rpx 40rpx;
		}
		.image {
			width:76rpx;
			height: 76rpx;
			border-radius: 50%;
			border: 3rpx solid #fff;
			margin-right: 20rpx;
		}
		.text {
			flex: 1;
			font-size: 22rpx;
			line-height: 30rpx;
			color: #89735B;
		}
		.name {
			margin-bottom: 2rpx;
			font-weight: bold;
			font-size: 30rpx;
			line-height: 42rpx;
			color: #865622;
		}
		.info {
			margin-top: 74rpx;
			font-size: 24rpx;
			color: #865622;
		}
	}
	.sys-head .bg {
		background: linear-gradient( 180deg, #121019 0%, #33333A 100%);
	}
	.right-section {
		width: 690rpx;
		height: 520rpx;
		margin: 0 auto;
		background-image: url("data:image/png;base64,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");
		background-size: cover;
		background-repeat: no-repeat;
		margin-top: -60rpx;
		.section-hd {
			padding-top: 34rpx;
			padding-bottom: 34rpx;	
			.title {
				width: 543rpx;
				font-size: 28rpx;
				line-height: 1.1;
				color: #FFEED2;
			}
			.iconfont {
				font-size: 34rpx;
				color: #89735B;
			}
		}
		.section-bd {
			padding: 35rpx 30rpx 15rpx;
			.item {
				width: 210rpx;
				margin-bottom: 30rpx;
				text-align: center;
			}
			.pic {
				width: 80rpx;
				height: 80rpx;
				border-radius: 100%;
			}
			.text {
				flex: 1;
				overflow: hidden;
				white-space: nowrap;
				font-size: 20rpx;
				line-height: 33rpx;
				color: #92846D;
			}
			.name {
				margin-bottom: 2rpx;
				font-size: 24rpx;
				line-height: 37rpx;
				color: #F4D7A3;
			}
		}
	}
	.type-section {
		position: relative;
		top: -20rpx;
		border-radius: 24rpx 24rpx 0 0;
		background-color: #FFFFFF;
		padding: 0 30rpx 50rpx;
		.title_bd {
			padding: 26rpx 40rpx 0 0;
			font-size: 24rpx;
			color: #797979;
			.bold {
				display: inline-block;
				margin-right: 14rpx;
				font-weight: bold;
				font-size: 32rpx;
				line-height: 45rpx;
				color: #333333;
				+view {
					display: inline-block;
				}
			}
			.time {
				margin-left: 14rpx;
				font-size: 24rpx;
				color: #AE5A2A;
			}
		}
		.scroll {
			white-space: nowrap;
		}
		.item {
			display: inline-flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 210rpx;
			height: 240rpx;
			border-radius: 12rpx;
			margin: 32rpx 20rpx 30rpx 0;
			border: 1px solid #CFCFCF;
			line-height: 42rpx;
			&:last-child {
				margin-right: 0;
			}
			&.on {
				border: 3rpx solid #FCC282;
				background-color: #FEF7EC;
				.new {
					color: #DBAA4D;
				}
			}
			.title{
				color: #282828;
				font-weight: bold;
				font-size: 30rpx;
				width: 180rpx;
				text-align: center;
			}
		}
		.new {
			margin-top: 22rpx;
			font-weight: 600;
			font-size: 34rpx;
			color: #E7BE7D;
			.num {
				font-size: 48rpx;
				line-height: 48rpx;
			}
		}
		.old {
			margin-top: 13rpx;
			text-decoration: line-through;
			font-size: 24rpx;
			color: #999999;
		}
		.info {
			margin-top: 13rpx;
			font-size: 24rpx;
		}
		.agree {
			font-size: 22rpx;
			text-align: center;
			color: #999999;
			margin-top: 30rpx;
			.link {
				display: inline-block;
			}
			.mark {
				color: #AE5A2A;
			}
		}
		.buy {
			height: 80rpx;
			border-radius: 12rpx;
			margin-top: 20rpx;
			background: linear-gradient(270deg, #E5BA62 0%, #ECCA7F 51%, #F3D99B 100%);
			font-size: 30rpx;
			font-weight: bold;
			line-height: 80rpx;
			text-align: center;
			color: #8A602E;
			border-radius:: 44rpx;
		}
		.cash {
			padding-top: 26rpx;
			padding-bottom: 29rpx;
			font-size: 28rpx;
			text-align: center;
			color: #754E19;
		}
	}
	.goods-section {
		margin-top: -20rpx;
		background: #ffffff left top/414px 215px no-repeat;
		background-image: linear-gradient(180deg, #FFF7E9 0%, #FFFFFF 100%);
		padding-bottom: calc(0rpx + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
		padding-bottom: calc(0rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
		.section-hd {
			padding: 26rpx 30rpx 0;
			font-weight: bold;
			font-size: 32rpx;
			line-height: 45rpx;
			color: #333333;
			text {
				color: #BCA376;
				font-size: 24rpx;
				font-weight: normal;
				margin-left: 15rpx;
			}
		}
		.section-bd {
			padding: 0 30rpx 0 7rpx;
			margin-top: 26rpx;
			.item {
				width: 215rpx;
				padding-bottom: 24rpx;
				margin-left: 23rpx;
			}
			/deep/image, /deep/uni-image, /deep/.easy-loadimage {
				width: 215rpx;
				height: 215rpx;
				border-radius: 16rpx;
			}
			.name {
				margin-top: 10rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				font-size: 26rpx;
				line-height: 37rpx;
				color: #282828;
			}
			.text_info{
				color: #92846D;
			}
			.svip-price {
				margin-top: 6rpx;
				font-size: 26rpx;
				color: #282828;
				font-weight: bold;
				image {
					width: 65rpx;
					height: 28rpx;
					margin-left: 6rpx;
					position:  relative;
					top: 5rpx;
				}
			}
			.shop-price {
				margin-top: 4rpx;
				font-size: 20rpx;
				color: #666666;
			}
		}
	}
</style>
