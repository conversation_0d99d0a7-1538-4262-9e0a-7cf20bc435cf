<template>
  <view class="exchange-detail">
    <!-- 顶部商品图片 -->
    <view class="product-img-box">
      <image class="product-img" src="https://dummyimage.com/128x128/3c9cff/fff" mode="aspectFit" />
    </view>
    <!-- 商品标题和价格 -->
    <view class="product-title">
      <view>
        <text class="name">施华蔻洗发水</text>
        <text class="price">88.6元+60积分</text>
      </view>
      <view class="product-desc">500ml*1瓶</view>
    </view>
    <!-- 使用方式 -->
    <view class="section">
      <text class="section-label">使用方式：</text>
      <text class="section-value">到店自提，不支持邮寄</text>
    </view>
    <!-- 适用门店 -->
    <view class="section">
      <view class="section-header">
        <text class="section-label">适用门店</text>
        <view class="section-link">
          查看全部
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>
      </view>
      <view class="store-info">
        <image class="store-img" src="https://dummyimage.com/98x98/3c9cff/fff" mode="aspectFill" />
        <view class="store-detail">
          <view class="store-name">
            御理清享金牛店
            <text class="iconfont icon-ic_rightarrow"></text>
          </view>
          <text class="store-addr">金牛区营门口街道银河北街232号</text>
          <text class="store-distance">距离2.5km</text>
        </view>
      </view>
    </view>
    <!-- 商品详情图片 -->
    <view class="detail-img-box">
      <view class="detail-img-title">商品详情</view>
      <image class="detail-img" src="https://dummyimage.com/375x400/3c9cff/fff" mode="widthFix" />
    </view>
    <!-- 底部兑换栏 -->
    <view class="exchange-bar">
      <text class="exchange-price">88.6元+60积分</text>
      <button class="exchange-btn">立即兑换</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.exchange-detail {
  min-height: 100vh;
  padding: 10rpx 30rpx 80rpx;
}
.product-img-box {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx;
  background-color: white;
  border-radius: 30rpx;
  margin-bottom: 10rpx;
}
.product-img {
  width: 100%;
  height: 380rpx;
  border-radius: 16rpx;
  background: #f5f5f5;
}
.product-title {
  padding: 40rpx 20rpx;
  font-size: 32rpx;
  background-color: white;
  border-radius: 30rpx;
}
.name {
  color: #222;
}
.price {
  font-weight: 600;
  font-size: 28rpx;
  color: #c9a063;
  margin-left: 16rpx;
}
.product-desc {
  color: #888;
  font-size: 24rpx;
  margin-top: 16rpx;
}
.section {
  background: #fafafa;
  margin: 0 0 16rpx 0;
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  margin-top: 10rpx;
}
.section-label {
  color: #222;
  font-weight: bold;
  font-size: 28rpx;
}
.section-value {
  color: #666;
  font-size: 28rpx;
  margin-left: 8rpx;
}
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.section-link {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  .icon-ic_rightarrow {
    font-size: 24rpx;
    font-weight: bold;
  }
}
.store-info {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}
.store-img {
  width: 98rpx;
  height: 98rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.store-detail {
  display: flex;
  flex-direction: column;
}
.store-name {
  color: #222;
  font-size: 28rpx;
  font-weight: bold;
  .icon-ic_rightarrow {
    font-size: 24rpx;
    font-weight: bold;
  }
}
.store-addr {
  color: #888;
  font-size: 24rpx;
}
.store-distance {
  font-weight: 400;
  font-size: 20rpx;
  color: #c9a063;
}
.detail-img-box {
  margin-top: 10rpx;
  padding: 40rpx 20rpx;
  background-color: white;
  border-radius: 30rpx;
}
.detail-img-title {
  font-weight: 600;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 40rpx;
}
.detail-img {
  width: 100%;
  border-radius: 16rpx;
}
.exchange-bar {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 32rpx;
  box-shadow: 0 -2rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}
.exchange-price {
  color: #e6a23c;
  font-size: 32rpx;
  font-weight: bold;
}
.exchange-btn {
  background: #c9a063;
  color: #fff;
  font-size: 28rpx;
  border-radius: 32rpx;
  padding: 0 48rpx;
  height: 64rpx;
}
</style>
