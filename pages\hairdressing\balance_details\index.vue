<template>
  <view class="exchange-store">
    <!-- 御享值 -->
    <view class="exchange-store__top">
      <view class="exchange-store__coin">
        <view class="">
          <view class="exchange-store__coin-label">我的余额</view>
          <view class="exchange-store__score">300</view>
        </view>
      </view>
    </view>
    <!-- 明细列表 -->
    <view class="details">
      <view class="details__title">消费明细</view>
      <view class="details__list">
        <view class="details__list__item">
          <view class="purpose">
            <view class="purpose-name">消费</view>
            <view class="purpose-time">2025-05-06 12:00:00</view>
          </view>
          <view class="price">-599</view>
        </view>
        <emptyPage
          title="暂无余额明细哦～"
          :noImage="`${domain}/static/images/noRecord.png`"
        ></emptyPage>
      </view>
    </view>
  </view>
</template>

<script>
import emptyPage from '@/components/emptyPage.vue'
import { HTTP_REQUEST_URL } from '@/config/app'
export default {
  components: { emptyPage },
  data() {
    return { domain: HTTP_REQUEST_URL }
  },
  onLoad() {},
  methods: {},
}
</script>

<style lang="scss" scoped>
.exchange-store {
  min-height: 100vh;
  background: linear-gradient(0deg, #f2f3f7 0%, #ffefd6 100%);
  padding: 28rpx;
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }
  &__coin {
    display: flex;
    align-items: center;
  }

  &__coin-label {
    font-size: 26rpx;
    color: #333;
    margin-right: 4rpx;
    display: flex;
    align-items: center;
    column-gap: 8rpx;
  }

  &__score {
    font-weight: 600;
    font-size: 36rpx;
    color: #333333;
  }
  .details {
    margin-top: 40rpx;
    padding: 40rpx 20rpx;
    background: #ffffff;
    border-radius: 30rpx;
    &__title {
      display: inline-block;
      font-size: 30rpx;
      font-weight: bold;
      background: url('https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
        no-repeat;
      background-size: 100% 34rpx;
      height: 54rpx;
      background-position: bottom center;
    }
    &__list {
      &__item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #f2f3f7;
        padding: 26rpx 0;
        .purpose {
          &-name {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            margin-bottom: 8rpx;
          }
          &-time {
            font-weight: 300;
            font-size: 22rpx;
            color: #999999;
          }
        }
        .price {
          font-weight: 600;
          font-size: 30rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
