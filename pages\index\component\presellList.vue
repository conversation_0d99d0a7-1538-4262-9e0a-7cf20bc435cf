<template>
	<view v-if="presellList.length > 0" class="presell-wrapper" :style="'padding-top:'+mbConfig+'rpx;'">
		<view class="presell-count" :class="'wrapper-count'+styleType" :style="'border-radius:'+bgStyle+'rpx'">
			<view class="spike-bd title-bd" :style="'border-radius:'+bgStyle+'rpx '+bgStyle+'rpx 0 0;background-image:url('+domain+'/static/images/presell_bg.png)'">
				<view class="title line1"><image class="title-img" :src="`${domain}/static/images/presell_title.png`"></image></view>
				<navigator v-if="!merId" url="/pages/activity/presell/index" class="more-btn" hover-class="none">
					进去逛逛
					<text class="iconfont icon-ic_rightarrow" hover-class="none"></text>
				</navigator>
			</view>
			<view class="wapper_count">
				<view class="spike-wrapper" :class="'wrapper'+styleType">
					<scroll-view v-if="styleType != 2" :class="'colum'+styleType" :scroll-x="styleType == 0 ? true : false" show-scrollbar="false">
						<navigator
							class="spike-item presell-item"
							v-for="(item, index) in presellList"
							:key="index"
							:url="'/pages/activity/presell_details/index?id=' + item.product_presell_id"
							hover-class="none"
						>
							<view class="img-box presell_imgBox" :class="'img-box'+conStyle">
								<easy-loadimage mode="widthFix" :image-src="item.product.image"></easy-loadimage>
								<view v-if="presellShow" class="box_bg" :style="'background-image: url('+domain+'/static/images/combination4.png)'">火热预定中</view>
							</view>
							<view v-if="priceShow || titleShow" class="info">
								<view class="price-box presell-price">
									<view :style="'color:'+themeColor" v-if="priceShow" class="price acea-row row-middle">
										<text>预售价:</text>
										<priceFormat :price="item.price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
									</view>
									<view v-if="titleShow" class="name line1">{{ item.store_name }}</view>
								</view>
							</view>
						</navigator>
					</scroll-view>
					<block v-else class="acea-row row-between-wrapper combination">
						<navigator 
							class="combination-item" 
							v-for="(item, index) in presellList" 
							v-if="index<=2"
							:key="index"
							:style="{ 'background-image': `url(${domain}/static/images/combination${index+1}.png)`,'border-radius':`${conStyle}rpx`}"
							:url="'/pages/activity/presell_details/index?id=' + item.product_presell_id"
							hover-class="none">
							<view class="info">
								<view class="price-box combination-price">
									<view v-if="titleShow" class="name line1">{{ item.store_name }}</view>
									<view :style="'color:'+themeColor" v-if="priceShow" class="price">
										<priceFormat :price="item.price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
									</view>
									<text v-if="presellShow" class="gocom_btn">
										去预定
										<text class="iconfont icon-ic_rightarrow"></text>
									</text>
								</view>
							</view>
							<view class="img-box" :class="'img-box'+conStyles">
								<easy-loadimage mode="widthFix" :image-src="item.product.image"></easy-loadimage>
							</view>
						</navigator>
						<navigator v-if="presellList.length == 1" 
							class="combination-item"
							hover-class="none" 
							:style="{ 'background-image': `url(${domain}/static/images/combination2.png)`}"
							:url="'/pages/activity/presell_details/index?id=' + presellList[0].product_presell_id"
							>
							<view class="info">
								<view class="price-box combination-price">
									<view v-if="titleShow" class="name line1">{{ presellList[0].store_name }}</view>
									<view :style="'color:'+themeColor" v-if="priceShow" class="price">
										<priceFormat :price="presellList[0].price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
									</view>
									<text v-if="presellShow" class="gocom_btn">
										去预定
										<text class="iconfont icon-ic_rightarrow"></text>
									</text>
								</view>
							</view>
							<view class="img-box" :class="'img-box'+conStyles">
								<easy-loadimage mode="widthFix" :image-src="presellList[0].product.image"></easy-loadimage>
							</view>
						</navigator>
						<navigator v-if="presellList.length == 1" 
							class="combination-item"
							hover-class="none" 
							:style="{ 'background-image': `url(${domain}/static/images/combination3.png)`}"
							:url="'/pages/activity/presell_details/index?id=' + presellList[0].product_presell_id"
							>
							<view class="info">
								<view class="price-box combination-price">
									<view v-if="titleShow" class="name line1">{{ presellList[0].store_name }}</view>
									<view :style="'color:'+themeColor" v-if="priceShow" class="price">
										<priceFormat :price="presellList[0].price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
									</view>
									<text v-if="presellShow" class="gocom_btn">
										去预定
										<text class="iconfont icon-ic_rightarrow"></text>
									</text>
								</view>
							</view>
							<view class="img-box" :class="'img-box'+conStyles">
								<easy-loadimage mode="widthFix" :image-src="presellList[0].product.image"></easy-loadimage>
							</view>
						</navigator>
						<navigator v-if="presellList.length == 2"
						class="combination-item"
						hover-class="none" 
						:style="{ 'background-image': `url(${domain}/static/images/combination3.png)`}"
						:url="'/pages/activity/presell_details/index?id=' + presellList[1].product_presell_id"
						>
							<view class="info">
								<view class="price-box combination-price">
									<view v-if="titleShow" class="name line1">{{ presellList[1].store_name }}</view>
									<view :style="'color:'+themeColor" v-if="priceShow" class="price">
										<priceFormat :price="presellList[1].price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
									</view>
									<text v-if="presellShow" class="gocom_btn">
										去预定
										<text class="iconfont icon-ic_rightarrow"></text>
									</text>
								</view>
							</view>
							<view class="img-box" :class="'img-box'+conStyles">
								<easy-loadimage mode="widthFix" :image-src="presellList[1].product.image"></easy-loadimage>
							</view>
						</navigator>
					</block>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
	
import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue';
import { getPresellData } from '@/api/api.js';
import { HTTP_REQUEST_URL } from '@/config/app';
export default {
	components:{
		easyLoadimage
	},
	name: 'presellList',
	props: {
		dataConfig: {
			type: Object,
			default: () => {}
		},
		merId: {
			type: String || Number,
			default: ''
		}	
	},
	data() {
		return {
			domain: HTTP_REQUEST_URL,
			presellList: [],
			mbConfig: this.dataConfig.mbConfig.val*2, //页面间距
			styleType: this.dataConfig.tabConfig.tabVal, //单行，多行，板块
			bgStyle: this.dataConfig.bgStyle.type ? 24 : 0,
			conStyle: this.dataConfig.conStyle.type ? 16 : 0,
			conStyles: this.dataConfig.conStyle.type,
			titleShow: this.dataConfig.titleShow.val,
			priceShow: this.dataConfig.priceShow.val,
			themeColor: this.dataConfig.themeColor && this.dataConfig.themeColor.color[0].item,
			presellShow: this.dataConfig.presellShow.val,
			diy_id: this.dataConfig.did,
			unique: this.dataConfig.timestamp,
		};
	},
	created() {},
	mounted() {
		this.getPresellProduct()
	},
	methods: {
		// 预售
		getPresellProduct() {
			let that = this;
			getPresellData({
				diy_id: that.diy_id,
				unique: that.unique,
				mer_id: that.merId,
				limit: that.styleType == 2 ? 3 : 12
			}).then(res => {
				that.presellList = res.data.list;
			}).catch(e => {});
		},
	}
};
</script>

<style scoped lang="scss">
@import '../style/main.scss';
.presell-wrapper{
	margin: 0 20rpx;
}
.spike-wrapper {
	box-sizing: border-box;
	&.wrapper2{
		padding: 0 20rpx 20rpx;
	}	
}
.presell-count {
	background-color: #fff;
	border-radius: 16rpx;
	box-shadow: 4rpx 2rpx 12rpx 2rpx rgba(0, 0, 0, 0.03);
}
.title-bd {
	margin-bottom: 0;
	padding: 24rpx 30rpx 30rpx 30rpx;
	border-radius: 16rpx 16rpx 0 0;
	background-size: 100%;
	background-repeat: no-repeat;
	.more-btn {
		top: 23rpx;
	}
}
.box_bg {
	position: absolute;
	bottom: 0;
	left: 0;
	text-align: center;
	width: 149rpx;
	height: 42rpx;
	line-height: 42rpx;
	background-repeat: no-repeat;
	background-size: cover;
	color: #fff;
	font-size: 22rpx;
}
.wapper_count {
	padding-left: 20rpx;
}
.wrapper-count2{
	.wapper_count{
		padding: 0;
	}
}
</style>
