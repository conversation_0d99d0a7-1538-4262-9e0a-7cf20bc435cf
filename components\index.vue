<template>
	<view>
		<view class='coupon-window' :class='window==true?"on":""'>
			<view class='couponWinList'>
				<view class='item acea-row row-between-wrapper' v-for="(item,index) in couponList" :key="index">
					<view class='money font-color'>¥<text class='num'>{{item.coupon_price}}</text></view>
					<view class='text'>
						<view class='name'>购物买{{item.use_min_price}}减{{item.coupon_price}}</view>
						<view>{{item.start_time ? item.start_time+'-' : ''}}{{item.end_time}}</view>
					</view>
				</view>
			</view>
			<view class='lid'>
				<navigator hover-class='none' url='/pages/users/user_get_coupon/index' class='bnt font-color'>立即领取</navigator>
				<view class='iconfont icon-ic_close1' @click="close"></view>
			</view>
		</view>
		<view class='mask' catchtouchmove="true" :hidden="window==false"></view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	export default {
		props: {
			window: {
				type: Boolean,
				default: false,
			},
			couponList: {
				type: Array,
				default: function() {
					return []
				},
			}
		},
		data() {
			return {

			};
		},
		methods: {
			close:function(){
				this.$emit('onColse');
			}
		}
	}
</script>

<style scoped lang="scss">
	.coupon-window {
		background-image: url(https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/1f9de202508022053184534.png);
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 630rpx;
		height: 649rpx;
		position: fixed;
		top: 20%;
		z-index: 99;
		left: 50%;
		margin-left: -305rpx;
		transform: translate3d(0, -200%, 0);
		transition: all .3s cubic-bezier(.25, .5, .5, .9);
	}

	.coupon-window.on {
		transform: translate3d(0, 0, 0);
	}

	.coupon-window .couponWinList {
		width: 480rpx;
		margin: 157rpx 0 0 60rpx;
		height: 415rpx;
		overflow: auto;
	}

	.coupon-window .couponWinList .item {
		width: 100%;
		height: 120rpx;
		background-color: #fff;
		position: relative;
		margin-bottom: 17rpx;
	}

	.coupon-window .couponWinList .item::after {
		content: '';
		position: absolute;
		width: 18rpx;
		height: 18rpx;
		border-radius: 50%;
		background-color: #f2443a;
		left: 25.5%;
		bottom: 0;
		margin-bottom: -9rpx;
	}

	.coupon-window .couponWinList .item::before {
		content: '';
		position: absolute;
		width: 18rpx;
		height: 18rpx;
		border-radius: 50%;
		background-color: #f2443a;
		left: 25.5%;
		top: 0;
		margin-top: -9rpx;
	}

	.coupon-window .couponWinList .item .money {
		width: 130rpx;
		border-right: 1px dashed #ddd;
		height: 100%;
		text-align: center;
		line-height: 120rpx;
		font-size: 26rpx;
		font-weight: bold;
	}

	.coupon-window .couponWinList .item .money .num {
		font-size: 40rpx;
	}

	.coupon-window .couponWinList .item .text {
		width: 349rpx;
		font-size: 22rpx;
		color: #999;
		padding: 0 29rpx;
		box-sizing: border-box;
	}

	.coupon-window .couponWinList .item .text .name {
		font-size: 26rpx;
		color: #282828;
		font-weight: bold;
		margin-bottom: 9rpx;
	}

	.coupon-window .lid {
		background-image: url('https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/dd4f6202508022054011681.png');
		background-repeat: no-repeat;
		background-size: 100% 100%;
		width: 573rpx;
		height: 224rpx;
		position: fixed;
		left: 50%;
		top: 20%;
		margin: 424rpx 0 0 -296rpx;
	}

	.coupon-window .lid .bnt {
		font-size: 29rpx;
		width: 440rpx;
		height: 80rpx;
		border-radius: 40rpx;
		background-color: #f9f1d3;
		text-align: center;
		line-height: 80rpx;
		font-weight: bold;
		margin: 98rpx auto 0 auto;
	}

	.coupon-window .lid .iconfont {
		color: #fff;
		font-size: 60rpx;
		text-align: center;
		margin-top: 87rpx;
	}
</style>
