<template>
  <view class="exchange-store">
    <!-- 御享值 -->
    <view class="exchange-store__top">
      <view class="exchange-store__coin">
        <image
          class="exchange-store__coin-img"
          src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/75da9202508011742187555.png"
          mode="aspectFill"
        />
        <view class="">
          <view class="exchange-store__coin-label">
            我的御享值
            <view class="rules center" @click="openRulesPopup">
              规则
              <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/2a7d4202508022106562977.png" class="rules-img" mode="widthFix"></image>
            </view>
          </view>
          <view class="exchange-store__score">{{ userInfo.integral ? userInfo.integral : 0 }}</view>
        </view>
      </view>
    </view>
    <!-- 明细列表 -->
    <view class="details">
      <view class="details__title">御享值明细</view>
      <view class="details__list">
        <view
          class="tip"
          v-if="userInfo.clear && userInfo.clear.status && userInfo.clear.nextClearIntegral > 0"
        >
          <text class="iconfont icon-shuoming"></text>
          <text class="tip-text">
            提示：您有部分积分将于{{ userInfo.clear.nextClearDay }}过期，请尽快使用！
          </text>
        </view>
        <block v-for="(item, index) in integralList" :key="index">
          <view class="details__list__item">
            <view class="purpose">
              <view class="purpose-name">{{ item.mark }}</view>
              <view class="purpose-time">{{ item.create_time }}</view>
            </view>
            <view>
              <block v-if="item.pm">
                <view class="">
                  <text v-if="item.status == 0" class="item-label">冻结中</text>
                  <view class="price add">+{{ item.number }}</view>
                </view>
              </block>
              <view class="price" v-else>-{{ item.number }}</view>
            </view>
          </view>
        </block>
        <view class="loadingicon acea-row row-center-wrapper" v-if="integralList.length > 0">
          <text class="loading iconfont icon-jiazai" :hidden="loading == false"></text>
          {{ loadTitle }}
        </view>
        <view v-if="integralList.length == 0">
          <emptyPage
            title="暂无积分记录哦～"
            :noImage="`${domain}/static/images/noRecord.png`"
          ></emptyPage>
        </view>
      </view>
    </view>
    <!-- 积分规则弹窗 -->
    <uni-popup ref="rulesPopup" type="bottom">
      <view class="rulesPopup">
        <view class="rulesPopup__banner">
          <image
            class="img"
            src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/147a6202506251647109502.png"
            mode="aspectFill"
          ></image>
          <text class="iconfont icon-ic_close1" @click="closeRulesPopup"></text>
        </view>
        <view class="bg-white">
          <view class="rulesPopup__title">御享值规则</view>
          <view class="rulesPopup__text">
            <jyf-parser :html="protocol" ref="article" :tag-style="tagStyle"></jyf-parser>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { getAgreementApi, getIntegralList, getIntegralInfo } from '@/api/user.js'
import parser from '@/components/jyf-parser/jyf-parser'
import emptyPage from '@/components/emptyPage.vue'
import { HTTP_REQUEST_URL } from '@/config/app'

export default {
  components: { 'jyf-parser': parser, emptyPage },
  data() {
    return {
      domain: HTTP_REQUEST_URL,
      protocol: '',
      page: 1,
      limit: 10,
      integralList: [],
      userInfo: {},
      loadend: false,
      loading: false,
      loadTitle: '加载更多',
      showProtocol: false,
      protocol: '',
      tagStyle: {
        img: 'width:100%;display:block;',
        video: 'width:100%;',
      },
      integral_status: true,
    }
  },
  onLoad() {
    this.getAgreement()
    this.loadIntegralList()
    this.loadUserInfo()
  },
  onReachBottom() {
    this.loadIntegralList()
  },
  methods: {
    openRulesPopup() {
      this.$refs.rulesPopup.open()
    },
    closeRulesPopup() {
      this.$refs.rulesPopup.close()
    },
    /**
     * 获取积分明细
     */
    loadIntegralList() {
      if (this.loading) return
      if (this.loadend) return
      this.loading = true
      this.loadTitle = ''
      getIntegralList({
        page: this.page,
        limit: this.limit,
      }).then(
        (res) => {
          let list = res.data.list,
            loadend = list.length < this.limit
          this.integralList = this.$util.SplitArray(list, this.integralList)
          this.page = this.page + 1
          this.loading = false
          this.loadend = loadend
          this.loadTitle = loadend ? '哼~😕我也是有底线的~' : '加载更多'
        },
        (res) => {
          this.loading = false
          this.loadTitle = '加载更多'
        },
      )
    },
    getAgreement() {
      getAgreementApi('sys_integral_rule').then((res) => {
        console.log('res', res)
        this.protocol = res.data.sys_integral_rule
      })
    },
    loadUserInfo() {
      getIntegralInfo()
        .then((res) => {
          this.userInfo = res.data
        })
        .catch((res) => {
          this.integral_status = false
          return this.$util.Tips(
            { title: res },
            {
              tab: 3,
              url: 1,
            },
          )
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.exchange-store {
  min-height: 100vh;
  background: linear-gradient(0deg, #f2f3f7 0%, #ffefd6 100%);
  padding: 28rpx;
  &__top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
  }
  &__coin {
    display: flex;
    align-items: center;
  }
  &__coin-img {
    width: 68rpx;
    height: 68rpx;
    margin-right: 18rpx;
    vertical-align: middle;
  }
  &__coin-label {
    font-size: 26rpx;
    color: #333;
    margin-right: 4rpx;
    display: flex;
    align-items: center;
    column-gap: 8rpx;
    .rules {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      margin-left: 10rpx;
    }
    .rules-img {
      width: 24rpx;
      height: 24rpx;
      margin-left: 10rpx;
    }
  }

  &__score {
    font-weight: 600;
    font-size: 36rpx;
    color: #333333;
  }
  .details {
    margin-top: 40rpx;
    padding: 40rpx 20rpx;
    background: #ffffff;
    border-radius: 30rpx;
    &__title {
      display: inline-block;
      font-size: 30rpx;
      font-weight: bold;
      background: url('https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
        no-repeat;
      background-size: 100% 34rpx;
      height: 54rpx;
      background-position: bottom center;
    }
    &__list {
      .tip {
        font-size: 25rpx;
        border-radius: 50rpx;
        background-color: #fff5e2;
        border: 1px solid #ffeac1;
        color: #c8a86b;
        padding: 10rpx 20rpx 10rpx 55rpx;
        box-sizing: border-box;
        margin-bottom: 24rpx;
        position: relative;
        margin: 10rpx 0;
        .iconfont {
          font-size: 35rpx;
          margin-right: 15rpx;
          position: absolute;
          top: 10rpx;
          left: 16rpx;
        }
      }

      &__item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1rpx solid #f2f3f7;
        padding: 26rpx 0;
        .purpose {
          &-name {
            font-weight: 500;
            font-size: 24rpx;
            color: #333333;
            margin-bottom: 8rpx;
          }
          &-time {
            font-weight: 300;
            font-size: 22rpx;
            color: #999999;
          }
        }
        .price {
          font-weight: 600;
          font-size: 30rpx;
          color: #333333;
        }
        .add {
          color: #16ac57 !important;
        }
      }
    }
  }
}
.bg-white {
  background-color: white;
}
.rulesPopup {
  &__banner {
    position: relative;
    .iconfont {
      position: absolute;
      right: 96rpx;
      top: 48rpx;
      color: #999999;
      font-size: 48rpx;
    }
    .img {
      width: 100%;
      height: 180rpx;
      vertical-align: bottom;
    }
  }
  &__title {
    padding-top: 10rpx;
    margin-top: -6rpx;
    text-align: center;
    font-weight: 600;
    font-size: 30rpx;
    color: #333333;
  }
  &__text {
    padding: 60rpx 30rpx;
    min-height: 600rpx;
  }
}
</style>
