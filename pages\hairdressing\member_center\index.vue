<template>
  <view class="member-center" v-if="memberInfo.uid">
    <!-- 会员等级Banner -->
    <view class="member-banner">
      <swiper
        class="swiper"
        :interval="interval"
        :duration="duration"
        previous-margin="50rpx"
        next-margin="50rpx"
        :current="currentSwiper"
        @change="onSwiperChange"
      >
        <swiper-item class="swiper__item" v-for="(item, index) in levelList" :key="item.id">
          <view class="swiper__item__wrap" :class="item.class" @click="toDetails">
            <view class="status">{{ getLevelStatus(item, index) }}</view>
            <view class="title">{{ item.brokerage_name }}</view>
            <view class="desc">成长值达{{ item.brokerage_rule.value }}即可升级</view>
            <view class="progress__title">
              成长值
              <text class="point">{{ memberInfo.member_value || 0 }}</text>
              /{{ item.brokerage_rule.value }}
            </view>
            <view class="progress__box">
              <view
                class="progress__line"
                :style="{
                  width: (memberInfo.member_value / item.brokerage_rule.value) * 100 + '%',
                }"
              ></view>
            </view>
            <image :src="item.icon" mode="widthFix" class="icon-img" />
          </view>
        </swiper-item>
      </swiper>
      <scroll-view class="welfare-list" scroll-x>
        <view class="welfare-item" v-for="(item, idx) in welfareList" :key="idx" @click="toDetails">
          <view class="welfare-item__icon">
            <image :src="item.icon" mode="widthFix" />
            <view
              class="welfare-item__mask"
              v-if="memberInfo.member.brokerage_level < item.brokerage_level"
            >
              <image
                src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/9d45f202508022020536808.png"
                class="welfare-item__lock"
                mode="widthFix"
              />
            </view>
          </view>
          <view class="welfare-item__title">{{ item.title }}</view>
        </view>
      </scroll-view>
      <!-- 滑块指示器 -->
      <view class="custom-swiper-indicator">
        <view class="custom-swiper-indicator__track">
          <view
            class="custom-swiper-indicator__bar"
            :style="{
              left: indicatorLeft,
            }"
          ></view>
        </view>
      </view>
    </view>
    <!-- 任务列表 -->
    <view class="wrap">
      <view class="privilege-tasks">
        <view class="privilege-tasks__title">获取更多御享值</view>
        <view class="privilege-tasks__list">
          <view class="privilege-tasks__item" v-for="(item, idx) in activeServices" :key="idx">
            <view class="privilege-tasks__item-icon center">
              <image
                class="privilege-tasks__item-icon-img"
                :src="item.icon"
                mode="widthFix"
              ></image>
            </view>
            <view class="privilege-tasks__item-info">
              <view class="privilege-tasks__item-title">
                {{ item.title }}
                <text class="subtitle" v-if="item.subtitle">
                  {{ item.subtitle }}
                </text>
              </view>
              <view class="privilege-tasks__item-desc">{{ item.info }}</view>
            </view>
            <button class="privilege-tasks__item-btn" @click="toPage(item)">
              {{ item.button }}
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getLevelLst } from '@/api/hairdressing.js'
import { memberInfo } from '@/api/user.js'
export default {
  data() {
    return {
      interval: 2000,
      duration: 500,
      levelList: [
        {
          id: 1,
          name: '初识',
          desc: '注册会员',
          brokerage_level: 1,
          icon: '',
          class: 'swiper-1',
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/be159202508011742187394.png',
        },
        {
          id: 2,
          name: '好友',
          desc: '成长值达1500即可升级',
          brokerage_level: 2,
          class: 'swiper-2',
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/d3765202508011742183207.png',
        },
        {
          id: 3,
          name: '密友',
          desc: '成长值达5000即可升级',
          brokerage_level: 3,
          class: 'swiper-3',
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/d4c09202508011742186667.png',
        },
        {
          id: 4,
          name: '挚友',
          desc: '成长值达10000即可升级',
          brokerage_level: 4,
          class: 'swiper-4',
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/3f691202508011742186288.png',
        },
      ],
      currentSwiper: 0,
      // 福利列表
      welfareList: [
        {
          id: 5,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/0ab83202508022017322332.png',
          title: '成长值获取使用',
          point: 0,
          brokerage_level: 1,
        },
        {
          id: 6,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/6306e202508022019387339.png',
          title: '升级礼',
          point: 0,
          brokerage_level: 1,
        },
        {
          id: 7,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/68716202508022020103973.png',
          title: '生日礼',
          point: 0,
          brokerage_level: 2,
        },
        {
          id: 8,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/3851d202508022019201872.png',
          title: '每月券包',
          point: 0,
          brokerage_level: 2,
        },
        {
          id: 9,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/aa1b3202508022018286734.png',
          title: '消费折上折',
          point: 0,
          brokerage_level: 2,
        },
        {
          id: 10,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/a0062202508022018584107.png',
          title: '成长值抵现金',
          point: 0,
          brokerage_level: 3,
        },
        {
          id: 11,
          icon: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/01182202508022018042630.png',
          title: '免排队',
          point: 0,
          brokerage_level: 4,
        },
      ],
      memberInfo: {},
      //当前等级
      currentLevel: 0,
    }
  },
  computed: {
    indicatorLeft() {
      return this.currentSwiper * (58 / this.levelList.length) + 'rpx'
    },
    // 获取所有启用的服务配置
    activeServices() {
      return this.$store.getters.getActiveServiceConfig
    },
    getLevelStatus() {
      return (item, index) => {
        // 判断是否为当前等级
        if (this.currentLevel === index) {
          return '当前等级'
        }
        // 判断是否已解锁（成长值达到要求）
        if (this.memberInfo.member.brokerage_level > item.brokerage_level) {
          return '已解锁'
        }
        return '未解锁'
      }
    },
  },
  onLoad() {
    this.loadLevelLst()
    this.getMemberInfo()
  },
  methods: {
    toDetails() {
      uni.navigateTo({
        url: '/pages/hairdressing/member_rights/index',
      })
    },
    // getLevelStatus(item, index) {
    //   // 判断是否为当前等级
    //   if (this.currentLevel === index) {
    //     return '当前等级'
    //   }
    //   // 判断是否已解锁（成长值达到要求）
    //   if (this.memberInfo.member.brokerage_level > item.brokerage_level) {
    //     return '已解锁'
    //   }
    //   return '未解锁'
    // },
    onSwiperChange(e) {
      this.currentSwiper = e.detail.current
    },
    loadLevelLst() {
      getLevelLst().then((res) => {
        // 更新等级列表
        res.data.list.forEach((item, index) => {
          if (this.levelList[index]) {
            this.$set(this.levelList, index, { ...this.levelList[index], ...item })
          }
          this.welfareList.forEach((welfare) => {
            if (welfare.brokerage_level === item.brokerage_level) {
              welfare.point = item.brokerage_rule.value
            }
          })
        })

        // 获取福利列表并匹配等级积分
        if (res.data.welfare_list) {
          this.welfareList = res.data.welfare_list.map((welfare) => {
            // 根据福利等级要求匹配对应的积分值
            const matchedLevel = res.data.list.find((level) => level.id === welfare.level_id)
            return {
              ...welfare,
              brokerage_rule: matchedLevel ? matchedLevel.brokerage_rule : { value: 0 },
            }
          })
        }
      })
    },
    // 获取会员信息
    async getMemberInfo() {
      const result = await memberInfo()
      if (result.status == 200) {
        this.memberInfo = result.data
        const index =
          this.levelList.findIndex(
            (item) => this.memberInfo.member.brokerage_name === item.brokerage_name,
          ) || 0

        this.currentSwiper = index
        // 更新当前等级
        this.currentLevel = index
      } else {
        this.$util.Tips({ title: result.msg })
      }
    },
    toPage(item) {
      uni.navigateTo({
        url: item.pagePath,
        fail: (fail) => {
          uni.switchTab({
            url: item.pagePath,
          })
        },
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.member-center {
  min-height: 100vh;
  background: linear-gradient(180deg, #352c1a 0%, #080913 100%);
}

.member-banner {
  margin-bottom: 40rpx;
  .swiper {
    .swiper__item {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .swiper__item__wrap {
      width: 610rpx;
      height: 268rpx;
      border-radius: 48rpx;
      position: relative;
      padding: 70rpx 40rpx 40rpx;
      .status {
        width: 157rpx;
        height: 48rpx;
        border-radius: 48rpx 0rpx 0rpx 0rpx;
        position: absolute;
        left: 0;
        top: 0;
        font-weight: 400;
        font-size: 22rpx;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .title {
        font-weight: 600;
        font-size: 48rpx;
        width: fit-content;
      }
      .desc {
        font-weight: 400;
        font-size: 24rpx;
        margin-top: 8rpx;
      }
      .progress__title {
        font-weight: 300;
        font-size: 18rpx;
        margin-top: 30rpx;
        .point {
          font-weight: bold;
        }
      }
      .progress__box {
        width: 296rpx;
        height: 8rpx;
        background: #f1faff;
        border-radius: 4rpx;
        margin-top: 4rpx;
      }
      .progress__line {
        height: 8rpx;
        border-radius: 4rpx;
      }
      .icon-img {
        width: 164rpx;
        height: 228rpx;
        position: absolute;
        top: 24rpx;
        right: 18rpx;
      }
    }
    .swiper-1 {
      background: linear-gradient(72deg, #f6f8fc 0%, #cad7f5 100%);
      .status {
        background: linear-gradient(90deg, #cbd8f5, #e7edfa);
        color: #6f89be;
      }
      .title {
        background: linear-gradient(39deg, #5e8cf7 0%, #9bc3f8 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .desc {
        color: #6f89be;
      }
      .progress__title {
        color: #6f89be;
      }
      .progress__line {
        background: #4370de;
      }
    }
    .swiper-2 {
      background: linear-gradient(90deg, #f6fcff 0%, #9ab3c0 100%);
      .status {
        background: linear-gradient(90deg, #859ea9, #dee9ee);
        color: #3d5f70;
      }
      .title {
        background: linear-gradient(39deg, #3d5f70 0%, #7992a2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .desc {
        color: #7992a2;
      }
      .progress__title {
        color: #3d5f70;
      }
      .progress__line {
        background: #3d5f70;
      }
    }
    .swiper-3 {
      background: linear-gradient(72deg, #f5dfb8 0%, #e6b759 100%);
      .status {
        background: linear-gradient(90deg, #e6b85c, #f0d39b);
        color: #8a5a23;
      }
      .title {
        background: linear-gradient(39deg, #8a5a23 0%, #d9a459 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .desc {
        color: #c5914b;
      }
      .progress__title {
        color: #8a5a23;
      }
      .progress__line {
        background: #8a5a23;
      }
    }
    .swiper-4 {
      background: linear-gradient(72deg, #f3efff 0%, #bab1f6 100%);
      .status {
        background: linear-gradient(90deg, #bbb2f6, #e0dafc);
        color: #3b2e95;
      }
      .title {
        background: linear-gradient(39deg, #3e2d9b 0%, #8f7feb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .desc {
        color: #958be5;
      }
      .progress__title {
        color: #3b2e95;
      }
      .progress__line {
        background: #3b2e95;
      }
    }
  }
}
.wrap {
  padding: 28rpx;
}
.privilege-tasks {
  background: linear-gradient(180deg, #393126, #1c1611);
  border-radius: 30rpx;
  padding: 40rpx 20rpx;

  &__title {
    font-size: 30rpx;
    color: white;
    font-weight: 600;
    margin-bottom: 40rpx;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  &__item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 18rpx;
    padding: 20rpx;
    gap: 10rpx;
  }

  &__item-icon {
    width: 68rpx;
    height: 68rpx;
    background: #fffbf4;
    border-radius: 12rpx;
    flex-shrink: 0;
    margin-right: 10rpx;
    &-img {
      width: 46rpx;
      height: 46rpx;
    }
  }

  &__item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4rpx;
  }
  &__item-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 2rpx;
    .subtitle {
      font-weight: 300;
      font-size: 22rpx;
      color: #999999;
      margin-left: 10rpx;
    }
  }
  &__item-desc {
    font-size: 22rpx;
    color: #c9a063;
    margin-top: 0rpx;
  }

  &__item-btn {
    background: #c9a063;
    color: #fff;
    font-size: 24rpx;
    border-radius: 24rpx;
    padding: 0 32rpx;
    height: 48rpx;
    line-height: 48rpx;
    font-weight: 600;
    flex-shrink: 0;
  }
}
.welfare-list {
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  white-space: nowrap;
  padding: 0 30rpx;
  margin-top: 40rpx;
  box-sizing: border-box;
}
.welfare-item {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  // width: 120rpx;
  margin-right: 80rpx;
  &:last-child {
    margin-right: 0;
  }
  &__icon {
    position: relative;
    width: 72rpx;
    height: 72rpx;
    margin-bottom: 10rpx;
    image {
      width: 100%;
      height: 100%;
    }
    .welfare-item__mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: 26rpx;
    }
    .welfare-item__lock {
      width: 100%;
      height: 100%;
    }
  }
  &__title {
    font-weight: 300;
    font-size: 22rpx;
    color: #999999;
  }
}
.custom-swiper-indicator {
  width: 78rpx;
  height: 6rpx;
  margin: 40rpx auto 0;
  display: flex;
  justify-content: center;
  align-items: center;
  &__track {
    width: 78rpx;
    height: 6rpx;
    background: rgba(255, 255, 255, 0.36);
    border-radius: 4rpx;
    position: relative;
    overflow: hidden;
  }
  &__bar {
    position: absolute;
    top: 0;
    width: 36rpx;
    height: 6rpx;
    background: white;
    border-radius: 4rpx;
    transition: left 0.3s;
    left: 0;
  }
}
</style>
