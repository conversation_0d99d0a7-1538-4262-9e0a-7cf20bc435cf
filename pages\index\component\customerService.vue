<template>
	<view style="touch-action: none;" v-if="service_type.sys_service_switch != 0 || merId != '0'">
		<view class="customerService" @touchmove.stop.prevent="setTouchMove" :style="{top:topConfig,right:prConfig+'rpx'}">
			<view class="pictrue" :url="'/pages/chat/customer_list/chat?mer_id='+merId" @click="customer">
				<image :src="logoConfig"></image>
			</view>
		</view>
	</view>
</template>
<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
import { configMap,getCustomer } from '@/utils/index';
	export default {
		name: 'customerService',
		props: {
			dataConfig: {
				type: Object,
				default: () => {}
			},
			merId: {
				type: [String, Number],
				default: '0'
			}
		},
		computed: configMap({service_type: {}}),
		data() {
			return {
				logoConfig: this.dataConfig.logoConfig.url,
				topConfig: (this.dataConfig.topConfig.val || 75)+'%',
				prConfig: this.dataConfig.prConfig.val*2
			};
		},
		created() {},
		methods: {
			setTouchMove(e) {
				var that = this;
				if (e.touches[0].clientY < 545 && e.touches[0].clientY > 66) {
					that.topConfig = e.touches[0].clientY+'px'
				}
			},
			customer() {
				let that = this;
				let sys = that.service_type
				let data = {
					type: sys.sys_service_switch,
					service_phone: sys.sys_phone,
					customer_url: sys.customer_url,
					customer_corpId: sys.customer_corpId,
					customer_link: sys.customer_link,
				}
				let url = `/pages/chat/customer_list/chat?merId=${that.merId}`
				getCustomer(data, url)
				
			},
		}
	}
</script>

<style lang="scss" scoped>
	.customerService {
		position: fixed;
		right: 20rpx;
		z-index: 999;
		/* #ifdef MP || APP-PLUS */
		top: 160rpx;
		right: 0;
		/* #endif */
		.pictrue {
			width: 86rpx;
			height: 86rpx;
			border-radius: 50%;
			image {
				width: 100%;
				height: 100%;
			}
		}
	}
</style>
