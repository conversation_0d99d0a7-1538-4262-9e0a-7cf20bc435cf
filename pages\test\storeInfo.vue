<template>
  <view class="test-page">
    <view class="header">
      <text class="title">门店信息管理测试</text>
    </view>
    
    <view class="section">
      <text class="section-title">当前门店信息</text>
      <view class="info-card">
        <text>门店ID: {{ storeId || '无' }}</text>
        <text>门店名称: {{ storeName || '无' }}</text>
        <text>是否有门店信息: {{ hasStoreInfo ? '是' : '否' }}</text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">操作按钮</text>
      <view class="button-group">
        <button @click="setTestStore" class="btn">设置测试门店</button>
        <button @click="getStoreData" class="btn">获取门店信息</button>
        <button @click="clearStore" class="btn">清除门店信息</button>
        <button @click="checkStore" class="btn">检查门店信息</button>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">完整门店信息</text>
      <view class="json-display">
        <text>{{ JSON.stringify(storeInfo, null, 2) }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import storeInfoMixin from '@/mixins/storeInfo.js'

export default {
  mixins: [storeInfoMixin],
  
  data() {
    return {
      testStoreInfo: {
        mer_id: 'test_001',
        mer_name: '测试美发店',
        mer_address: '测试地址123号',
        mer_banner: 'https://example.com/banner.jpg',
        distance: '500m',
        mer_take_day: ['一', '二', '三', '四', '五', '六'],
        mer_take_time: ['09:00', '21:00']
      }
    }
  },
  
  methods: {
    setTestStore() {
      this.setStoreInfo(this.testStoreInfo)
      uni.showToast({
        title: '门店信息已设置',
        icon: 'success'
      })
    },
    
    getStoreData() {
      const storeInfo = this.getStoreInfo()
      console.log('获取到的门店信息:', storeInfo)
      uni.showModal({
        title: '门店信息',
        content: `门店ID: ${storeInfo.mer_id || '无'}\n门店名称: ${storeInfo.mer_name || '无'}`,
        showCancel: false
      })
    },
    
    clearStore() {
      this.clearStoreInfo()
      uni.showToast({
        title: '门店信息已清除',
        icon: 'success'
      })
    },
    
    checkStore() {
      const result = this.checkStoreInfo('当前没有门店信息')
      console.log('检查结果:', result)
    }
  }
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.section {
  margin-bottom: 40rpx;
  
  .section-title {
    display: block;
    font-size: 28rpx;
    font-weight: bold;
    color: #666;
    margin-bottom: 20rpx;
  }
}

.info-card {
  background: white;
  padding: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  text {
    display: block;
    margin-bottom: 10rpx;
    font-size: 26rpx;
    color: #333;
  }
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  
  .btn {
    background: #007aff;
    color: white;
    border: none;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
  }
}

.json-display {
  background: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
  
  text {
    font-family: monospace;
    font-size: 22rpx;
    color: #333;
    white-space: pre-wrap;
    word-break: break-all;
  }
}
</style>
