---
description: 
globs: 
alwaysApply: false
---
# 项目结构

本文档提供了该uni-app项目的高级概述。

## 主要目录

- `[api/](mdc:api)`: 包含所有API请求的定义。每个文件通常对应一个业务模块。
- `[components/](mdc:components)`: 存放可复用的Vue组件。
- `[pages/](mdc:pages)`: 包含应用的所有页面。每个子目录代表一个页面或一组相关页面。
- `[static/](mdc:static)`: 存放静态资源，如图片、字体和图标。
- `[store/](mdc:store)`: 包含Vuex状态管理的模块。
- `[utils/](mdc:utils)`: 存放工具函数和辅助模块。

## 关键文件

- `[main.js](mdc:main.js)`: 应用的入口文件。
- `[App.vue](mdc:App.vue)`: 应用的根Vue组件。
- `[pages.json](mdc:pages.json)`: uni-app的页面配置文件，用于定义页面路由、导航栏样式等。
- `[manifest.json](mdc:manifest.json)`: 应用的配置文件，用于设置应用名称、图标、权限等。
- `[vue.config.js](mdc:vue.config.js)`: Vue CLI的配置文件。

