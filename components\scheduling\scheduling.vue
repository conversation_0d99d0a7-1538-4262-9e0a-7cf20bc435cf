<template>
  <view>
    <uni-popup ref="popup" type="bottom">
      <view class="popup-wrap ios-pb">
        <view class="popup-bg"></view>
        <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/a2961202508011742182431.png" mode="scaleToFill" class="icon" />
        <view class="popup-container">
          <!-- 顶部装饰 -->
          <view class="popup-header">
            <image class="avatar" :src="stylist.avatar" />
            <view class="info">
              <view class="name">{{ stylist.nickname }}</view>
              <RoleTag :role="stylist.level_name" />
              <view class="score">满意值 {{ stylist.manyizhi }}</view>
            </view>
          </view>
          <!-- 日期选择 -->
          <view class="date-row">
            <view
              v-for="(item, idx) in dateList"
              :key="item.date"
              :class="['date-item', { active: idx === activeDateIdx }]"
              @click="selectDate(idx, item)"
            >
              <view>{{ item.label }}</view>
              <view>{{ item.dateStr }}</view>
            </view>
          </view>
          <!-- 休息日 or 时间表 -->
          <view class="content">
            <template v-if="!timeTable.length">
              <view class="rest-box">
                <image class="rest-img" src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/a6fd220250801174218995.png" />
              </view>
            </template>
            <template>
              <view class="time-table">
                <view
                  v-for="(slot, idx) in timeTable"
                  :key="slot.time"
                  :class="['time-slot', slot.status, { full: slot.user_id > 0 }]"
                >
                  {{ slot.time }}
                  <template v-if="slot.user_id > 0">
                    <text class="full-tag">已满</text>
                  </template>
                </view>
              </view>
            </template>
          </view>
          <!-- 底部按钮 -->
          <button class="confirm-btn" @click="closePopup">我知道了</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import RoleTags from '@/components/roleTags/roleTags'
import { getAppointment } from '@/api/hairdressing.js'
export default {
  name: 'scheduling',
  components: { RoleTags },
  data() {
    return {
      stylist: {},
      dateList: [],
      dates: [],
      activeDateIdx: 0,
      timeTable: [],
    }
  },
  created() {
    this.generateDates()
  },
  methods: {
    generateDates() {
      const dates = []
      const today = new Date()

      for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(today.getDate() + i)

        // 格式化日期
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const dateStr = `${year}-${month}-${day}`

        // 获取星期几
        const weekDays = ['日', '一', '二', '三', '四', '五', '六']
        const weekDay = weekDays[date.getDay()]

        // 生成标签
        let label = ''
        if (i === 0) {
          label = '今天'
        } else if (i === 1) {
          label = '明天'
        } else {
          label = `周${weekDay}`
        }

        dates.push({
          date: dateStr,
          label: label,
          dateStr: `${month}/${day}`,
          fullDate: date,
        })
      }

      this.dates = dates
    },
    selectDate(idx, item) {
      console.log('🚀 ~ selectDate ~ item:', item)
      this.activeDateIdx = idx
      this.timeTable = item.data.length ? item.data[0].timetable : []
    },
    closePopup() {
      this.stylist = {}
      this.$refs.popup.close()
    },
    open(stylist) {
      this.stylist = stylist
      this.activeDateIdx = 0
      console.log('🚀 ~ open ~ stylist:', stylist)
      // 获取dates数组的首项和尾项日期
      const startDate = this.dates.length > 0 ? this.dates[0].date : ''
      const endDate = this.dates.length > 0 ? this.dates[this.dates.length - 1].date : ''
      const params = {
        service_id: stylist.service_id,
        day: `${startDate} ~ ${endDate}`,
      }

      getAppointment(params).then((res) => {
        console.log('🚀 ~ getAppointment ~ res:', res)
        // 处理返回的数据，将其转换为与dates结构匹配的格式
        this.processAppointmentData(res.data)

        this.$refs.popup.open()
      })
    },
    processAppointmentData(appointmentData) {
      // 将API返回的数据处理成与dates结构相匹配的格式
      const processedDates = this.dates.map((dateItem) => {
        // 查找对应日期的预约数据
        const appointmentItem = appointmentData.find((item) => item.day === dateItem.date)

        return {
          ...dateItem,
          data: appointmentItem ? appointmentItem.data : [],
          rest: appointmentItem ? false : true, // 如果没有数据则认为是休息日
        }
      })
      this.dateList = processedDates
      console.log('🚀 ~ processAppointmentData ~ processedDates:', processedDates)
      this.timeTable = this.dateList[0].data.length ? this.dateList[0].data[0].timetable : []
    },
  },
}
</script>

<style lang="scss" scoped>
.popup-wrap {
  position: relative;
}
.popup-bg {
  width: 737rpx;
  height: 227rpx;
  background: #333333;
  border-radius: 62rpx 148rpx 62rpx 62rpx;
  position: absolute;
  left: 0;
  top: -38rpx;
  z-index: 1;
}
.icon {
  width: 158rpx;
  height: 156rpx;
  position: absolute;
  z-index: 10;
  left: 96rpx;
  top: -106rpx;
}
.popup-container {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 70rpx 30rpx 140rpx 30rpx;
  min-height: 600rpx;
  position: relative;
  z-index: 8;
}
.popup-header {
  display: flex;
  align-items: center;
  position: relative;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 40rpx;
  .avatar {
    width: 88rpx;
    height: 88rpx;
    border-radius: 50%;
    margin-right: 20rpx;
  }
  .info {
    display: flex;
    align-items: center;
    gap: 10rpx;
    .name {
      font-size: 32rpx;
      font-weight: 600;
      color: #222;
    }
    .score {
      color: #a27630;
      font-size: 24rpx;
      background: linear-gradient(-34deg, #ffefd7, #ffffff, #e2c99e);
      border-radius: 6rpx;
    }
  }
}
.date-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .date-item {
    flex: 1;
    text-align: center;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    &.active {
      color: #c9a063;
    }
  }
}
.content {
  min-height: 430rpx;
}
.rest-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 130rpx;
  .rest-img {
    width: 154rpx;
    height: 164rpx;
  }
}
.time-table {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx 44rpx;
  margin-top: 40rpx;
  .time-slot {
    width: 140rpx;
    height: 44rpx;
    line-height: 44rpx;
    text-align: center;
    border: 1rpx solid #c9a063;
    border-radius: 12rpx;
    color: #c9a063;
    font-size: 26rpx;
    background: #fff;
    &.selected {
      background: #b88a4b;
      color: #fff;
    }
    &.full {
      background: #fff;
      color: #cccccc;
      border: 1rpx solid #cccccc;
      position: relative;
    }
    .full-tag {
      width: 46rpx;
      height: 22rpx;
      background: #cccccc;
      border-radius: 0rpx 11rpx 0rpx 11rpx;
      position: absolute;
      font-weight: 400;
      font-size: 18rpx;
      color: #ffffff;
      right: 0;
      top: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.confirm-btn {
  width: 90%;
  margin: 48rpx auto 0 auto;
  background: #b88a4b;
  color: #fff;
  font-size: 32rpx;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border: none;
}
</style>
