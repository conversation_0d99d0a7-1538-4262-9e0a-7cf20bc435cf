---
description: 
globs: 
alwaysApply: false
---
# 造型师页面

本文档概述了造型师页面的结构和功能，该页面位于 `[pages/hairstylist/index.vue](mdc:pages/hairstylist/index.vue)`。

## 概述

造型师页面展示了门店列表，并在每个门店内展示了造型师列表。它允许用户查看造型师的详细信息、他们提供的服务，并提供预约选项。

## 文件结构

主组件是 `pages/hairstylist/index.vue`。

### 关键组件

- `[components/customTab/index.vue](mdc:components/customTab/index.vue)`: 用于导航的自定义标签栏组件。

### API 依赖

该页面从以下 API 端点获取数据：

- `getNavigation` from `[api/public.js](mdc:api/public.js)`: 获取自定义标签栏的导航数据。
- `storeMerchantList` from `[api/store.js](mdc:api/store.js)`: 检索门店列表。
- `serviceUserList` from `[api/user.js](mdc:api/user.js)`: 检索指定门店的造型师列表。

## 数据

组件的数据包括：

- `storeList`: 门店对象的数组。每个门店对象包含一个 `hairstylists` 列表。
- `params`: 用于获取门店列表的参数，包括分页和位置数据。

该页面目前使用 `loadMockData` 方法进行开发和测试，该方法用示例数据填充 `storeList`。在生产环境中，它会从 API 获取数据。

