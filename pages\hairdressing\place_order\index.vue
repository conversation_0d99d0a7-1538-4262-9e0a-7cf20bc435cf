<template>
	<view class="page-container">
		<view class="top-wrap">
			<view class="card-box store-box">
				<view class="store-info">
					<image
						:src="storeInfo.mer_avatar"
						mode="aspectFill"
						class="store-cover"
					></image>
					<view class="store-content">
						<view class="name">
							{{ storeInfo.mer_name }}
							<text class="iconfont icon-ic_rightarrow"></text>
						</view>
						<view class="address">
							{{ storeInfo.mer_address }}
							<text class="iconfont icon-icon_copy"></text>
						</view>
						<view class="distance">距离你{{ storeInfo.distance }}</view>
					</view>
				</view>
			</view>
			<!-- 服务项目 -->
			<!-- <view class="service-item">
        <view class="service-item__name">发型师</view>
        <view class="service-item__desc">
          <text>请选择发型师</text>
           <view class="info">
          <image
            src="https://dummyimage.com/28x28/3c9cff/bfa"
            class="avatar"
            mode="widthFix"
          ></image>
          <text class="name">郑老师</text>
          <view class="">
            <RoleTag />
          </view>
        </view>
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>
      </view>
      <view class="service-item">
        <view class="service-item__name">预约时间</view>
        <view class="service-item__desc">
          请选择预约时间
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>
      </view> -->
		</view>
		<!--  -->
		<view class="main-section">
			<!-- 左侧菜单 -->
			<view class="left-menu">
				<view
					v-for="(item, idx) in getServerTabs"
					:key="item.store_category_id"
					:class="['menu-item', { active: selectedTab === item.store_category_id }]"
					@click="handleTabClick(item)"
				>
					<text class="label">
						{{ item.cate_name }}
					</text>
					<text v-if="item.num" class="menu-badge">
						{{ item.num }}
					</text>
				</view>
			</view>
			<!-- 右侧商品列表 -->
			<view class="right-content">
				<scroll-view scroll-y="true" class="scroll-Y">
					<block v-for="(prod, index) in productList" :key="index">
						<view class="product-card" v-if="prod.cate_id === selectedTab">
							<view class="product-card__top">
								<image :src="prod.image" class="product-img" mode="aspectFill" />
								<view class="product-info">
									<view class="product-title">{{ prod.store_name }}</view>
									<view class="product-tags">
										<text class="tag">标签</text>
										<text class="tag">缺少</text>
									</view>
									<view class="service-items">
										<view class="price-info">
											<view class="price">
												<text>¥</text>
												{{ prod.product.price }}
											</view>
											<view class="discount">{{ prod.discount }}折</view>
											<view class="original-price">
												¥{{ prod.product.ot_price }}
											</view>
										</view>
									</view>
								</view>
							</view>
							<view class="product-card__footer">
								<view class="product-op">
									<button
										v-if="prod.num > 0"
										class="op-btn minus center"
										@click="reduce(prod)"
									>
										<text class="iconfont icon-ic_Reduce"></text>
									</button>
									<text class="count center">
										{{ prod.num > 0 ? prod.num : '' }}
									</text>
									<button class="op-btn center" @click="add(prod)">
										<text class="iconfont icon-ic_increase"></text>
									</button>
								</view>
							</view>
						</view>
					</block>
				</scroll-view>
			</view>
		</view>
		<!-- 底部栏 -->
		<view class="bottom-bar ios-pb">
			<view class="cart-icon center">
				<text class="cart-badge center" v-if="totalCount > 0">{{ totalCount }}</text>
				<image
					src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/001c2202508022027256353.png"
					mode="widthFix"
					class="shopcart"
				></image>
			</view>
			<view class="price-info">
				<text class="total-price">{{ totalPrice }}</text>
				<text class="origin">￥{{ totalOrigin }}</text>
				<view class="check-details center" @tap="showDetailPopup">
					查看明细
					<text
						class="iconfont"
						:class="show ? 'icon-ic_downarrow' : 'icon-ic_uparrow'"
					></text>
				</view>
			</view>
			<button class="submit-btn" hover-class="btnHoverClass" @click="handlePlaceOrder">
				下单
			</button>
		</view>
		<!-- 查看明细 -->
		<uni-popup ref="detailPopup" type="bottom" @change="onPopupChange">
			<view class="cart-popup">
				<view class="cart-popup__header">
					<view class="cart-popup__title">已加购服务({{ totalCount }})</view>
					<view class="cart-popup__clear center" @tap="clearCart">
						<text class="iconfont icon-ic_delete"></text>
						清空
					</view>
					<text class="cart-popup__close iconfont icon-ic_close" @tap="closePopup"></text>
				</view>
				<scroll-view scroll-y class="cart-popup__content">
					<block v-for="(item, index) in productList" :key="index">
						<view class="cart-popup__item" v-if="item.num > 0">
							<image
								:src="item.image"
								mode="aspectFill"
								class="cart-popup__item-img"
							></image>
							<view class="cart-popup__item-info">
								<view class="cart-popup__item-title">{{ item.store_name }}</view>
								<view class="cart-popup__item-price">
									<view class="price-info">
										<view class="price">
											<text>¥</text>
											{{ item.product.price }}
										</view>
										<view class="discount">{{ item.discount }}折</view>
										<view class="original-price">
											¥{{ item.product.ot_price }}
										</view>
									</view>
									<view class="product-op">
										<button
											v-if="item.num > 0"
											class="op-btn minus center"
											@tap="reduce(item)"
										>
											<text class="iconfont icon-ic_Reduce"></text>
										</button>
										<text class="count center">{{ item.num }}</text>
										<button class="op-btn center" @tap="add(item)">
											<text class="iconfont icon-ic_increase"></text>
										</button>
									</view>
								</view>
							</view>
						</view>
					</block>
				</scroll-view>
				<view class="cart-popup__footer">
					<view class="cart-popup__footer__title">结算明细</view>
					<view class="cart-popup__detail-item">
						<view class="label">服务总价</view>
						<view class="value">¥{{ totalOrigin }}</view>
					</view>
					<!-- <view class="cart-popup__detail-item">
            <view class="label">储值卡余额</view>
            <view class="value">
              <text class="value disabled">暂无可用</text>
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view> -->
					<!-- <view class="cart-popup__detail-item">
            <view class="label">优惠券</view>
            <view class="value">
              <text class="value disabled">暂无可用</text>
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view> -->
					<!-- <view class="cart-popup__detail-item">
            <view class="label">成长值</view>
            <view class="value">
              <text class="value red">减5元</text>
              <text class="iconfont icon-ic_rightarrow"></text>
            </view>
          </view> -->
					<view class="cart-popup__detail-item total">
						<view class="label total-info-label">费用合计</view>
						<view class="value">
							<view class="total-info">
								<!-- <text class="discount-tag">已优惠¥5</text> -->
								<text class="final-price">¥{{ totalPrice }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import { getServerCategorys, getStoreServicesList } from '@/api/hairdressing.js'
import { changeCartNum, cartDel } from '@/api/order.js'
import RoleTag from '@/components/roleTags/roleTags.vue'
import { toLogin } from '@/libs/login.js'
import { mapGetters } from 'vuex'
import { postCartAdd } from '@/api/store.js'
export default {
	components: { RoleTag },
	data() {
		return {
			stylistId: 0,
			stylist: [
				{
					id: 0,
					label: '到店分配',
					avatar: 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/ae331202508011741087381.png',
				},
				{
					id: 1,
					name: '郑老师',
					avatar: 'https://dummyimage.com/119x115/3c9cff/fff',
					satisfaction: '9.8',
					role: '店长',
				},
				{
					id: 2,
					name: '郑老师',
					avatar: 'https://dummyimage.com/119x115/3c9cff/fff',
					satisfaction: '9.8',
					role: '店长',
				},
				{
					id: 3,
					name: '郑老师',
					avatar: 'https://dummyimage.com/119x115/3c9cff/fff',
					satisfaction: '9.8',
					role: '店长',
				},
			],
			dateId: 0,
			date: [
				{
					id: 0,
					week: '今天',
					day: '05.27',
				},
				{
					id: 1,
					week: '明天',
					day: '05.28',
				},
				{
					id: 2,
					week: '周四',
					day: '05.29',
				},
				{
					id: 3,
					week: '周五',
					day: '05.30',
				},
				{
					id: 4,
					week: '周六',
					day: '05.31',
				},
				{
					id: 5,
					week: '周日',
					day: '06.01',
				},
			],
			timeId: 1,
			time: [
				{
					id: 0,
					time: '09:00',
					disabled: true,
				},
				{
					id: 1,
					time: '09:30',
					disabled: false,
				},
				{
					id: 2,
					time: '10:00',
					disabled: false,
				},
				{
					id: 3,
					time: '10:30',
					disabled: false,
				},
				{
					id: 4,
					time: '11:00',
					disabled: false,
				},
				{
					id: 5,
					time: '11:30',
					disabled: false,
				},
				{
					id: 6,
					time: '12:00',
					disabled: false,
				},
				{
					id: 7,
					time: '12:30',
					disabled: false,
				},
				{
					id: 8,
					time: '13:00',
					disabled: true,
				},
				{
					id: 9,
					time: '13:30',
					disabled: false,
				},
				{
					id: 10,
					time: '14:00',
					disabled: false,
				},
				{
					id: 11,
					time: '14:30',
					disabled: false,
				},
				{
					id: 12,
					time: '15:00',
					disabled: false,
				},
				{
					id: 13,
					time: '15:30',
					disabled: false,
				},
				{
					id: 14,
					time: '16:00',
					disabled: false,
				},
				{
					id: 15,
					time: '16:30',
					disabled: false,
				},
				{
					id: 16,
					time: '17:00',
					disabled: true,
				},
				{
					id: 17,
					time: '17:30',
					disabled: false,
				},
			],
			selectedTab: '',
			productList: [],
			show: false,
			storeInfo: {},
			serverTabs: [],
		}
	},
	computed: {
		...mapGetters(['isLogin', 'uid']),
		cart() {
			return this.productList.filter((product) => product.num > 0)
		},
		totalCount() {
			return this.cart.length
		},
		totalNum() {
			return this.cart.reduce((sum, item) => sum + item.num, 0)
		},
		totalOrigin() {
			return this.cart.reduce((sum, item) => sum + item.product.ot_price * item.num, 0)
		},
		totalPrice() {
			return this.cart.reduce((sum, item) => sum + item.product.price * item.num, 0)
		},
		getServerTabs() {
			//计算当前分类下num大于 0的数量
			return this.serverTabs.map((item) => {
				item.num = this.productList.filter(
					(product) => product.cate_id === item.store_category_id && product.num > 0,
				).length
				return item
			})
		},
	},
	onLoad(options) {
		if (options.info) {
			this.storeInfo = JSON.parse(options.info)
			this.loadHotTabs()
			this.loadStoreServicesList()
		}
	},
	methods: {
		//加载服务分类
		loadHotTabs() {
			getServerCategorys().then((res) => {
				this.selectedTab = res.data[0].store_category_id
				this.serverTabs = res.data
			})
		},
		//加载 服务列表
		loadStoreServicesList() {
			getStoreServicesList(this.storeInfo.mer_id).then((res) => {
				res.data.list.forEach((item) => {
					item.discount = ((item.product.price / item.product.ot_price) * 10).toFixed(1)
					item.num = 0
					item.cart_id = null
				})
				this.productList = res.data.list
			})
		},
		add(v) {
			console.log('🚀 ~ add ~ v:', v)
			//是否登录
			if (this.isLogin === false) {
				toLogin()
			} else {
				if (v.is_taocan === 1) {
					uni.navigateTo({
						url: `/pages/goods_details/index?id=${v.product_id}&spid=${this.uid}`,
					})
					return
				}
				if (v.cart_id) {
					v.num++
					this.handleCartNum(v)
				} else {
					this.goCat(v)
				}
			}
		},
		handleCartNum(v) {
			changeCartNum(v.cart_id, {
				cart_num: v.num,
			})
				.then((res) => {
					console.log('🚀 ~ .then ~ res:', res)
				})
				.catch((error) => {
					this.$util.Tips({
						title: error,
					})
				})
		},

		reduce(v) {
			//是否登录
			if (this.isLogin === false) {
				toLogin()
			} else {
				if (v.num > 0) v.num -= 1
				if (v.num === 0) {
					//删除购物车
					this.handleDeleCart(v)
				}
				if (v.cart_id) {
					this.handleCartNum(v)
				}
			}
		},
		handleDeleCart(v) {
			cartDel({
				cart_id: [v.cart_id],
			})
				.then((res) => {
					v.num = 0
					v.cart_id = null
				})
				.catch((error) => {
					this.$util.Tips({
						title: error,
					})
				})
		},
		onStylist(id) {
			this.stylistId = id
		},
		onDate(id) {
			this.dateId = id
		},
		onTime(item) {
			if (item.disabled) return
			this.timeId = item.id
		},
		closePopup() {
			this.$refs.detailPopup.close()
		},
		clearCart() {
			this.cart = {}
		},
		showDetailPopup() {
			if (this.show) {
				this.$refs.detailPopup.close()
			} else {
				this.$refs.detailPopup.open()
			}
		},
		/**
		 * 立即购买
		 */
		goBuy(e) {
			if (this.isLogin === false) {
				toLogin()
			} else {
				this.goCat(true)
			}
		},
		/*
		 * 加入购物车
		 */
		goCat(v) {
			console.log('🚀 ~ goCat ~ v:', v)
			let q = {
				is_new: 0,
				product_id: v.product.product_id,
				cart_num: v.num + 1,
				product_attr_unique: v.sku[''].unique,
				// source: '',
				product_type: 0,
				spread_id: '',
			}
			postCartAdd(q)
				.then((res) => {
					v.num++
					v.cart_id = res.data.cart_id
					console.log('🚀 ~ .then ~ res:', res)
				})
				.catch((res) => {
					return this.$util.Tips({
						title: res,
					})
				})
		},
		onPopupChange(e) {
			this.show = e.show
		},
		handlePlaceOrder() {
			if (this.totalCount <= 0) return this.$util.Tips({ title: '请选择服务' })
			if (this.show === false) return this.showDetailPopup()
			//下单逻辑
			const selectValue = this.cart.map((item) => item.cart_id)
			if (selectValue.length > 0) {
				// uni.redirectTo({
				// 	url: '/pages/users/order_confirm/index?cartId=' + selectValue.join(','),
				// })
				uni.redirectTo({
					url: '/pages/hairdressing/order_addcart/order_addcart?cartId=' + selectValue.join(','),
				})
			} else {
				return this.$util.Tips({
					title: '请选择产品',
				})
			}
		},
		handleTabClick(item) {
			this.selectedTab = item.store_category_id
		},
	},
}
</script>

<style lang="scss" scoped>
.page-container {
	width: 100%;
	height: 100vh;
	background-color: #f7f7f7;
	display: flex;
	flex-direction: column;
}
.top-wrap {
	padding: 14rpx 30rpx;
}
.store-box {
	background: linear-gradient(90deg, #ffffff 0%, #ffffff 35%, #fffaf2 100%) !important;
	.store-info {
		display: flex;
		align-items: center;
		.store-cover {
			width: 96rpx;
			height: 96rpx;
			border-radius: 8rpx;
		}
		.store-content {
			flex: 1;
			margin-left: 20rpx;
			.name {
				font-size: 30rpx;
				font-weight: bold;
				.iconfont {
					color: #999;
					margin-left: 10rpx;
					font-size: 24rpx;
					font-weight: bold;
				}
			}

			.address,
			.distance {
				font-weight: 400;
				font-size: 20rpx;
				margin-top: 8rpx;
				.iconfont {
					margin-left: 8rpx;
					color: #cecece;
					font-size: 22rpx;
				}
			}
			.distance {
				color: #c9a063;
			}
		}
	}
}
.card-box {
	border-radius: 30rpx;
	padding: 30rpx 20rpx 20rpx;
	margin-bottom: 10rpx;
	background-color: #ffffff;

	.title {
		font-size: 30rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
	}
	.store-info {
		display: flex;
		align-items: center;
		.store-cover {
			width: 96rpx;
			height: 96rpx;
			border-radius: 8rpx;
		}
		.store-content {
			flex: 1;
			margin-left: 20rpx;
			.name {
				font-size: 30rpx;
				font-weight: bold;
				.iconfont {
					color: #999;
					margin-left: 10rpx;
					font-size: 24rpx;
					font-weight: bold;
				}
			}

			.address,
			.distance {
				font-weight: 400;
				font-size: 20rpx;
				margin-top: 8rpx;
				.iconfont {
					margin-left: 8rpx;
					color: #cecece;
					font-size: 22rpx;
				}
			}
			.distance {
				color: #c9a063;
			}
		}
	}
}
.service-item {
	padding: 40rpx 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background-color: white;
	border-radius: 30rpx;
	margin-bottom: 10rpx;
	&__name {
		font-weight: 500;
		font-size: 28rpx;
		color: #333333;
	}
	&__desc {
		font-weight: 400;
		font-size: 26rpx;
		color: #333333;
		display: flex;
		align-items: center;
		gap: 8rpx;
		.iconfont {
			font-size: 26rpx;
		}
		.info {
			display: flex;
			align-items: center;
			gap: 10rpx;
			.avatar {
				width: 28rpx;
				height: 28rpx;
				border-radius: 50%;
			}
			.name {
				font-weight: 300;
				font-size: 26rpx;
				color: #333333;
			}
		}
	}
}
.booking-page {
	background: #fafafa;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}
.top-section {
	background: #fff;
	padding: 24rpx 32rpx 0 32rpx;
	border-radius: 0 0 30rpx 30rpx;
}
.row {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;
	.label {
		color: #999;
		width: 140rpx;
	}
	.value {
		display: flex;
		align-items: center;
	}
}

.avatar {
	width: 56rpx;
	height: 56rpx;
	border-radius: 50%;
	margin-right: 18rpx;
}
.role {
	background: #f9e5c7;
	color: #b88c43;
	font-size: 20rpx;
	border-radius: 10rpx;
	padding: 4rpx 12rpx;
	margin-left: 10rpx;
}
.time-select {
	color: #bbb;
}
.main-section {
	display: flex;
	flex: 1;
	background: #ffffff;
}
.left-menu {
	width: 168rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	background: #fafafa;
}
.menu-item {
	width: 100%;
	padding: 40rpx 0;
	font-weight: 400;
	font-size: 26rpx;
	color: #666666;
	position: relative;
	text-align: center;
}
.menu-item.active {
	background: linear-gradient(0deg, #ffffff 0%, #ffffff 35%, #fffaf2 100%);
	border-radius: 30rpx;
	color: #333333;
	font-size: 28rpx;
	font-weight: bold;
	.label {
		display: inline-block;
		height: 54rpx;
		background: url('https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/3fa21202506271056124625.png')
			no-repeat;
		background-size: 100% 34rpx;
		background-position: bottom center;
	}
}
.menu-badge {
	position: absolute;
	top: 12rpx;
	right: 38rpx;
	font-size: 22rpx;
	background: #333333;
	color: #fff;
	border-radius: 50%;
	width: 30rpx;
	height: 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.right-content {
	flex: 1;
	padding: 44rpx 20rpx;
}
.product-card {
	margin-bottom: 40rpx;
	&__top {
		display: flex;
		// align-items: center;
	}
	&__footer {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #fafafa;
	}
}

.product-img {
	width: 148rpx;
	height: 148rpx;
	border-radius: 12rpx;
	margin-right: 20rpx;
}
.product-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}
.product-title {
	font-weight: 500;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	margin-bottom: 10rpx;
}
.product-tags .tag {
	font-weight: 400;
	font-size: 20rpx;
	color: #927d6b;
	background: #f4efe0;
	border-radius: 6rpx;
	padding: 4rpx;
	margin-right: 20rpx;
}
.service-items {
	display: flex;
	align-items: center;
	margin: 20rpx 0;
	.price-info {
		display: flex;
		align-items: center;
		flex-wrap: nowrap;
		white-space: nowrap;

		.price {
			font-size: 32rpx;
			font-weight: bold;
			color: #a01c1c;

			text {
				font-size: 22rpx;
			}
		}

		.discount {
			font-size: 20rpx;
			color: #a01c1c;
			padding: 2rpx 6rpx;
			margin: 0 10rpx;
			background: #f7e9e9;
			border-radius: 3rpx;
			position: relative;
			&::before {
				content: '';
				position: absolute;
				left: -6rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 0;
				height: 0;
				border-style: solid;
				border-width: 8rpx 8rpx 8rpx 0;
				border-color: transparent #f7e9e9 transparent transparent;
			}
		}

		.original-price {
			font-size: 22rpx;
			color: #cccccc;
			text-decoration: line-through;
		}
	}
}
.product-op {
	display: flex;
	align-items: center;
	margin-top: 8rpx;
	gap: 18rpx;
}
.op-btn {
	width: 36rpx;
	height: 36rpx;
	background: #c9a063;
	border-radius: 50%;
	color: white;
	padding: 0;
	.iconfont {
		font-size: 26rpx;
	}
}
.minus {
	border: 1px solid #c9a063;
	background: transparent;
	color: #c9a063;
	.iconfont {
		color: #c9a063 !important;
	}
}
.count {
	font-weight: 400;
	font-size: 32rpx;
	color: #333333;
}
.bottom-bar {
	// height: 98rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #fff;
	border-top: 1rpx solid #f2f2f2;
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 999;
	padding: 28rpx 30rpx;
}
.cart-icon {
	position: relative;
	width: 74rpx;
	height: 74rpx;
	background: #c9a063;
	border-radius: 50%;
	margin-right: 48rpx;
	flex-shrink: 0;
	.shopcart {
		width: 48rpx;
		height: 42rpx;
	}
}
.cart-badge {
	position: absolute;
	top: -10rpx;
	right: -14rpx;
	width: 26rpx;
	height: 26rpx;
	background: #333333;
	border-radius: 50%;
	border: 2px solid #ffffff;
	font-weight: 500;
	font-size: 16rpx;
	color: #ffffff;
}
.price-info {
	display: flex;
	align-items: baseline;
	flex-shrink: 0;
}
.total-price {
	color: #e23e30;
	font-size: 34rpx;
	font-weight: 700;
	margin-right: 10rpx;
	&::before {
		content: '￥';
		font-size: 22rpx;
	}
}
.check-details {
	font-weight: 400;
	font-size: 24rpx;
	color: #c9a063;
	margin-left: 24rpx;
	gap: 10rpx;
	.iconfont {
		font-size: 24rpx;
	}
}
.bottom-bar .origin {
	color: #aaa;
	font-size: 24rpx;
	text-decoration: line-through;
}
.submit-btn {
	flex: 1;
	background: #dcb365;
	color: #fff;
	border-radius: 40rpx;
	padding: 16rpx 0;
	margin-left: 40rpx;
	font-weight: 400;
	font-size: 30rpx;
	color: #ffffff;
}
.scroll-Y {
	height: 100%;
}
.cart-popup {
	min-height: 60vh;
	background: #fff;
	padding: 40rpx 40rpx 128rpx;
	padding-bottom: calc(128rpx + env(safe-area-inset-bottom)) !important;
	padding-bottom: calc(128rpx + constant(safe-area-inset-bottom)) !important;
	border-radius: 30rpx 30rpx 0 0;

	&__header {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	&__title {
		font-weight: 600;
		font-size: 28rpx;
		color: #333333;
	}

	&__clear {
		margin-left: 16rpx;
		font-size: 26rpx;
		color: #999;
		display: flex;
		align-items: center;

		.iconfont {
			font-size: 26rpx;
			margin-right: 10rpx;
			color: #a8a8a8;
		}
	}

	&__close {
		color: #cccccc;
		position: absolute;
		top: -60rpx;
		font-size: 42rpx;
		right: 30rpx;
	}

	&__content {
		width: 100%;
		max-height: 40vh;
		padding: 40rpx 0;
		.price-info {
			.price {
				color: #a01c1c;
				font-size: 28rpx;
				font-weight: 500;

				text {
					font-size: 24rpx;
				}
			}

			.discount {
				font-size: 20rpx;
				color: #a01c1c;
				background: #f7e9e9;
				padding: 2rpx 8rpx;
				margin: 0 10rpx;
				border-radius: 4rpx;
				position: relative;
				&::before {
					content: '';
					position: absolute;
					left: -6rpx;
					top: 50%;
					transform: translateY(-50%);
					width: 0;
					height: 0;
					border-style: solid;
					border-width: 8rpx 8rpx 8rpx 0;
					border-color: transparent #f7e9e9 transparent transparent;
				}
			}

			.original-price {
				font-size: 24rpx;
				color: #cccccc;
				text-decoration: line-through;
			}
		}
	}

	&__item {
		display: flex;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f7f7f7;

		&-img {
			width: 98rpx;
			height: 98rpx;
			border-radius: 8rpx;
			margin-right: 20rpx;
		}

		&-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		&-title {
			font-size: 26rpx;
			font-weight: 500;
			font-size: 26rpx;
			color: #333333;
		}

		&-price {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 10rpx;
		}
	}

	&__footer {
		&__title {
			font-weight: 600;
			font-size: 28rpx;
			color: #333333;
			margin-bottom: 38rpx;
		}
	}

	&__detail-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 30rpx;
		font-size: 26rpx;
		.label {
			font-weight: 400;
			color: #333333;
		}
		.total-info-label {
			font-weight: 600;
			font-size: 28rpx;
		}

		.value {
			color: #999;
			display: flex;
			align-items: center;

			&.disabled {
				color: #999;
			}

			&.red {
				color: #a01c1c;
			}
		}

		.iconfont {
			margin-left: 8rpx;
			color: #999;
			font-size: 24rpx;
		}

		&.total {
			border-top: 2rpx solid #fafafa;

			.total-info {
				display: flex;
				align-items: center;
				gap: 16rpx;
			}

			.discount-tag {
				font-size: 24rpx;
				color: #a01c1c;
			}

			.final-price {
				font-size: 32rpx;
				font-weight: bold;
				color: #333333;
			}
		}
	}
}
</style>
