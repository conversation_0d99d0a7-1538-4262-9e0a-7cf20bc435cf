<template>
  <view class="page-wrapper">
    <view class="wrap-content">
      <view class="payment-status">
        <view class="acea-row row-middle row-center mb-10">
          <image
            src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/fba80202508022102566387.png"
            class="icon-img"
            v-if="order_pay_info.paid != 0 || order_pay_info.pay_type == 7"
            mode="scaleToFill"
          />
          <view class="iconfont icon-iconfontguanbi icon" v-else></view>
          <view class="status">
            {{ payResult }}
          </view>
        </view>
        <view class="acea-row row-center t-color">
          <view
            v-if="
              order_pay_info.pay_type == 1 ||
              order_pay_info.pay_type == 2 ||
              order_pay_info.pay_type == 3
            "
            class="itemCom"
          >
            微信支付
          </view>
          <view
            v-else-if="order_pay_info.pay_type == 4 || order_pay_info.pay_type == 5"
            class="itemCom"
          >
            支付宝支付
          </view>
          <view v-else-if="order_pay_info.pay_type == 7" class="itemCom">线下支付</view>
          <view v-else class="itemCom">余额支付</view>
          <view class="itemCom">{{ order_pay_info.pay_price }}</view>
        </view>
        <view class="acea-row row-center t-color">
          <view>御享值支付</view>
          <view class="itemCom">{{ order_pay_info.integral }}</view>
        </view>
      </view>
      <view class="product-list__title">更多商品</view>
      <view class="product-list" v-if="goodList.length">
        <view
          class="product-item"
          v-for="(item, index) in goodList"
          :key="item.product_id"
          @click="goGoodsDetail(item)"
        >
          <view class="pictrue">
            <image :src="item.image"></image>
            <view v-if="item.stock == 0" class="sell_out">已兑完</view>
          </view>
          <view class="info">
            <view class="title line1">{{ item.store_name }}</view>
            <view class="acea-row price-count">
              <view class="sales">{{ parseFloat(Number(item.price).toFixed(2)) }}元</view>
              <text class="price">+{{ item.ot_price }}</text>
              <text class="count-text">积分</text>
              <button hover-class="button-hover" class="exchange-btn">兑换</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="bottom-bar">
      <view class="item" @click="goIndex">
        <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/74975202508022021165931.png" mode="widthFix" class="icon"></image>
        <view class="text">回首页</view>
      </view>
      <!-- 打电话 -->
      <button hover-class="button-hover" class="btn center" @click="goOrderDetails">
        查看订单
      </button>
    </view>
  </view>
</template>

<script>
// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
import { getPayOrder } from '@/api/order.js'
import { getProductHotx } from '@/api/store.js'
import { mapGetters } from 'vuex'
import { toLogin } from '@/libs/login.js'
import { HTTP_REQUEST_URL } from '@/config/app'
import { configMap } from '@/utils'
import { getIntegralGoodsList } from '@/api/points_mall'
export default {
  components: {},
  filters: {
    timeYMD: function (value) {
      if (value) {
        var newDate = /\d{4}-\d{1,2}-\d{1,2}/g.exec(value)
        return newDate[0]
      }
    },
  },
  data() {
    return {
      domain: HTTP_REQUEST_URL,
      orderId: '',
      order_type: '',
      order_pay_info: {},
      order_id: '',
      group_order_id: '',
      status: 0,
      msg: '',
      couponList: [], //优惠券列表
      isOpen: false, //展开
      moneyBg: '/static/images/couponBg',
      text: '展开更多',
      timer: null,
      payResult: '正在查询支付结果...',
      loading: false, //是否加载中
      loadend: false, //是否加载完毕
      loadTitle: '加载更多', //提示语
      hotScroll: false,
      hotPage: 1,
      hotLimit: 10,
      hostProduct: [],
      goodList: [],
    }
  },
  computed: configMap({ recommend_switch: 0 }, mapGetters(['isLogin', 'viewColor', 'keyColor'])),
  onShow: function () {
    // 获取当前页面栈
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    // 获取最新参数
    const options = currentPage.options
    console.log('最新参数:', options)
    if (!options.order_id)
      return this.$util.Tips(
        {
          title: '缺少参数无法查看订单支付状态',
        },
        {
          tab: 3,
          url: 1,
        },
      )
    // 使用最新参数更新数据
    this.orderId = options.order_id
    this.order_type = options.order_type
    this.status = options.status || 0
    this.msg = options.msg || ''
  },
  onLoad: function (options) {
    // if (!options.order_id) return this.$util.Tips({
    // 	title: '缺少参数无法查看订单支付状态'
    // }, {
    // 	tab: 3,
    // 	url: 1
    // });
    // this.orderId = options.order_id;
    // this.order_type = options.order_type;
    // this.status = options.status || 0;
    // this.msg = options.msg || '';
    if (this.isLogin) {
      this.refreshData()
      this.getIntegralGoods()
    } else {
      toLogin()
    }
  },
  onHide: function () {
    clearInterval(this.timer)
    this.timer = null
  },
  methods: {
    refreshData() {
      this.timer = setInterval(() => {
        this.getOrderPayInfo()
      }, 1000)
    },
    // 优惠券展开
    bindMore() {
      this.isOpen = !this.isOpen
      this.text = this.text == '展开更多' ? '收起' : '展开更多'
    },
    /**
     *
     * 支付完成查询支付状态
     *
     */
    getOrderPayInfo: function () {
      let that = this
      getPayOrder(that.orderId)
        .then((res) => {
          that.$set(that, 'order_pay_info', res.data)
          that.$set(that, 'order_id', res.data.orderList[0]['order_id'])
          that.$set(that, 'group_order_id', res.data.orderList[0]['group_order_id'])

          that.couponList = res.data.give_coupon
          that.order_type = res.data.activity_type
          if (that.order_type == 20) {
            that.payResult = res.data.paid == 1 ? '兑换成功 ' : '支付失败'
          } else {
            that.payResult =
              res.data.paid == 1
                ? '兑换成功 '
                : res.data.pay_type == 7
                ? '订单创建成功'
                : '支付失败'
          }
          uni.setNavigationBarTitle({
            title:
              res.data.paid == 1 ? '兑换成功 ' : res.data.pay_type == 7 ? '未支付' : '支付失败',
          })
          if (res.data.paid == 1 || res.data.pay_type == 7) {
            clearInterval(this.timer)
            this.timer = null
          }
        })
        .catch((err) => {
          clearInterval(this.timer)
          uni.hideLoading()
          this.$util.Tips(
            {
              title: err,
            },
            {
              tab: 3,
              url: 1,
            },
          )
        })
    },
    /**
     * 去首页关闭当前所有页面
     */
    goIndex: function (e) {
      uni.switchTab({
        url: '/pages/index/index',
      })
    },
    // 去参团页面；
    goPink: function (id) {
      uni.navigateTo({
        url: '/pages/activity/combination_status/index?id=' + id,
      })
    },
    /**
     *
     * 去订单详情页面
     */
    goOrderDetails: function (e) {
      let that = this
      if (that.order_type == 20) {
        uni.navigateTo({
          url: `/pages/points_mall/integral_order_details?order_id=${that.order_id}`,
        })
      } else {
        if (that.order_pay_info.paid == 0) {
          if (that.order_pay_info.orderList.length == 1) {
            uni.navigateTo({
              url: `/pages/order_details/stay?order_id=${that.group_order_id}`,
            })
          } else {
            uni.navigateTo({
              url: '/pages/users/order_list/index?status=0',
            })
          }
        } else {
          if (that.order_pay_info.orderList.length == 1) {
            uni.navigateTo({
              url: `/pages/order_details/index?order_id=${that.order_id}`,
            })
          } else {
            uni.navigateTo({
              url: '/pages/users/order_list/index?status=1',
            })
          }
        }
      }
    },
    //积分商品列表
    getIntegralGoods() {
      let that = this
      if (that.loadend) return
      if (that.loading) return
      that.loading = true
      that.loadTitle = ''
      getIntegralGoodsList(that.where)
        .then((res) => {
          let list = res.data.list
          let goodList = that.$util.SplitArray(list, that.goodList)
          let loadend = list.length < that.where.limit
          that.loadend = loadend
          that.loading = false
          that.loadTitle = loadend ? '已全部加载' : '加载更多'
          that.$set(that, 'goodList', goodList)
        })
        .catch((err) => {
          that.loading = false
          uni.showToast({
            title: err,
            icon: 'none',
          })
        })
    },
    // 去商品详情
    goGoodsDetail(item) {
      uni.navigateTo({
        url: `/pages/points_mall/integral_goods_details?id=${item.product_id}`,
      })
    },
  },
  onReachBottom() {
    this.getHostProduct()
  },
  destroyed() {
    clearInterval(this.timer)
    this.timer = null
  },
  deactivated() {
    clearInterval(this.timer)
    this.timer = null
  },
}
</script>

<style lang="scss">
.page-wrapper {
  --view-theme: #c9a063;
  background-color: #fafafa;
  padding: 30rpx;
  min-height: 100vh;
  &::after {
    display: block;
    content: ' ';
    width: 100%;
    height: 646rpx;
    background: linear-gradient(0deg, #fafafa 0%, #ffefd6 84%, #ffefd6 100%);
    position: absolute;
    left: 0;
    top: 0;
  }
}
.wrap-content {
  position: relative;
  z-index: 9;
}
.payment-status {
  padding: 70rpx 0;
  .status {
    font-weight: 600;
    font-size: 37rpx;
    color: #c9a063;
  }
  .icon {
    color: var(--view-theme);
    margin-right: 16rpx;
  }
  .icon-img {
    width: 36rpx;
    height: 36rpx;
    margin-right: 16rpx;
  }
}
.payment-status .icon {
  &.icon-duihao2 {
    background-color: var(--view-theme);
    border: 3px solid #fff1f1;
  }
}
.t-color {
  color: var(--view-theme) !important;
}
.product-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 30rpx;
  &__title {
    font-weight: 600;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 36rpx;
    height: 54rpx;
    background: url('https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/22cb6202506231642544308.png')
      no-repeat;
    background-size: 100% 34rpx;
    background-position: bottom center;
    width: fit-content;
  }
  .product-item {
    position: relative;
    width: 330rpx;
    background: #fff;
    border-radius: 10rpx;
    margin-bottom: 20rpx;
    .pictrue {
      position: relative;
      width: 100%;
      height: 330rpx;
      .sell_out {
        display: flex;
        width: 150rpx;
        height: 150rpx;
        align-items: center;
        justify-content: center;
        border-radius: 100%;
        background: rgba(0, 0, 0, 0.6);
        color: #fff;
        font-size: 30rpx;
        position: absolute;
        top: 50%;
        left: 50%;
        margin: -75rpx 0 0 -75rpx;
        &::before {
          content: '';
          display: block;
          width: 140rpx;
          height: 140rpx;
          border-radius: 100%;
          border: 1px dashed #fff;
          position: absolute;
          top: 5rpx;
          left: 5rpx;
        }
      }
    }
    image {
      width: 100%;
      height: 340rpx;
      border-radius: 18rpx;
    }
    .info {
      padding: 20rpx;
      .title {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
      }
      .price-count {
        margin-top: 8rpx;
        align-items: baseline;
        font-weight: 500;
      }
      .price {
        color: var(--view-theme);
        font-size: 28rpx;
      }
      .count-text {
        font-size: 22rpx;
        color: var(--view-theme);
      }
      .sales {
        font-size: 28rpx;
        color: var(--view-theme);
      }
      .exchange-btn {
        margin-left: auto;
        font-weight: 400;
        font-size: 26rpx;
        color: #ffffff;
        padding: 6rpx 20rpx;
        border-radius: 20rpx;
        background: var(--view-theme);
      }
    }
  }
}
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid #f0f0f0;
  gap: 30rpx;
  .item {
    text-align: center;
    color: #666666;
    margin-left: 30rpx;
    .icon {
      width: 42rpx;
      height: 42rpx;
    }
    .text {
      font-weight: 400;
      font-size: 20rpx;
      color: #999999;
      margin-top: 6rpx;
    }
  }
  .btn {
    // width: 428rpx;
    flex: 1;
    height: 78rpx;
    background: #c9a063;
    border-radius: 39rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
  }
}
.mb-10 {
  margin-bottom: 10rpx;
}
</style>
