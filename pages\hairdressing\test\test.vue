<template>
  <view style="padding: 20rpx">
    <input v-model="search" placeholder="搜索icon名称" class="icon-search" />
    <view class="icon-demo">
      <view v-for="icon in filteredIcons" :key="icon" class="icon-item">
        <view class="icon-wrap">
          <text :class="['iconfont', icon]" />
          <text class="icon-name">{{ icon }}</text>
        </view>
        <button class="copy-btn center" hover-class="btnHoverClass" size="mini" @click="copy(icon)">
          copy
        </button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      search: '',
      icons: [
        'icon-icon_talk_2',
        'icon-zhuanshudaogou',
        'icon-mendianyeji1',
        'icon-zhuanshukehu1',
        'icon-yejidingdan1',
        'icon-ic_jinxingzhong',
        'icon-ic_sort2',
        'icon-ic_jindu2',
        'icon-ic_jindu1',
        'icon-ic_user2',
        'icon-yijujue',
        'icon-pintuanchenggong',
        'icon-pintuanshibai',
        'icon-tuikuanzhong1',
        'icon-tuikuanshibai',
        'icon-tuikuanchenggong',
        'icon-tuikuanzhong11',
        'icon-yiwancheng',
        'icon-yituikuan11',
        'icon-daituihuo11',
        'icon-shenqingzhong',
        'icon-yiquxiao',
        'icon-tuihuozhong',
        'icon-shenhezhong1',
        'icon-dingdanguanli',
        'icon-kefujilu',
        'icon-dingdanhexiao',
        'icon-shangjiaguanli',
        'icon-ic_key',
        'icon-iconfontguanbi',
        'icon-duihao2',
        'icon-ic_friends',
        'icon-ic_camera2',
        'icon-ic_order1',
        'icon-ic_QRcode2',
        'icon-ic_ranking',
        'icon-ic_statistics',
        'icon-ic_crown2',
        'icon-ic_yilingqu',
        'icon-a-ic_ShoppingBag2',
        'icon-a-ic_picture1',
        'icon-ic_pencil',
        'icon-ic_leaf',
        'icon-ic_card',
        'icon-ic_ban1',
        'icon-ic_fapiao',
        'icon-ic_left',
        'icon-ic_location2',
        'icon-ic_menu3',
        'icon-ic_close2',
        'icon-a-ic_ShoppingCart23',
        'icon-ic_more2',
        'icon-ic_share1',
        'icon-ic_love',
        'icon-a-ic_crown',
        'icon-a-ic_wechatpay',
        'icon-a-ic_offlinepay',
        'icon-a-ic_alipay',
        'icon-ic_list',
        'icon-a-ic_alipay2',
        'icon-ic_home',
        'icon-ic_electromobile',
        'icon-ic_shop1',
        'icon-ic_talk_2',
        'icon-ic_love_2',
        'icon-ic_camera1',
        'icon-icon_ban',
        'icon-icon_clock-2',
        'icon-ic_promotion',
        'icon-icon_card',
        'icon-a-icon_phonenumber',
        'icon-a-icon_contentclassification',
        'icon-a-icon_Scoreincrease',
        'icon-icon_copy',
        'icon-icon_picture',
        'icon-icon_question_2',
        'icon-icon_sale',
        'icon-icon_retern',
        'icon-icon_question',
        'icon-icon_video',
        'icon-icon_sort-2',
        'icon-icon_right',
        'icon-icon_transmit',
        'icon-icon_playback',
        'icon-a-icon_ShoppingBag',
        'icon-icon_menu',
        'icon-icon_review',
        'icon-icon_Like_2',
        'icon-icon_sort',
        'icon-icon_transmit-2',
        'icon-icon_team',
        'icon-icon_tip',
        'icon-icon_WeChat_1',
        'icon-icon_Link',
        'icon-icon_talk_2-2',
        'icon-ic_receiving1',
        'icon-icon_Smile',
        'icon-icon_talk',
        'icon-icon_comment',
        'icon-a-icon_nopicture',
        'icon-icon_date',
        'icon-icon_Alipay',
        'icon-icon_clock1',
        'icon-icon_statistics',
        'icon-ic_truck',
        'icon-a-icon_trailcar',
        'icon-a-icon_cornermark',
        'icon-icon_clock',
        'icon-ic_Phone',
        'icon-icon_Location',
        'icon-ic_location51',
        'icon-ic_Selected',
        'icon-ic_order',
        'icon-ic_staging',
        'icon-ic_user1',
        'icon-ic_commodity',
        'icon-ic_Scan',
        'icon-ic_more',
        'icon-ic_notes',
        'icon-ic_batch',
        'icon-ic_location5',
        'icon-ic_send',
        'icon-ic_expression',
        'icon-ic_picture',
        'icon-ic_tip1',
        'icon-ic_coupon',
        'icon-huiyuandengji',
        'icon-ic_daipingji2',
        'icon-ic_daifukuan2',
        'icon-ic_daifahuo2',
        'icon-ic_daifahuo',
        'icon-ic_daituikuan2',
        'icon-ic_daituikuan',
        'icon-ic_daishouhuo',
        'icon-ic_daifukuan',
        'icon-ic_daishouhuo2',
        'icon-ic_daipingjia',
        'icon-ic_daishouhuo1',
        'icon-ic_daifukuan12',
        'icon-ic_daifahuo11',
        'icon-ic_daituikuan1',
        'icon-ic_daipingjia1',
        'icon-a-ic_tanhao1',
        'icon-ic_lock',
        'icon-ic_message1',
        'icon-ic_transmit1',
        'icon-ic_wechat',
        'icon-ic_apple',
        'icon-a-jiantou11',
        'icon-ic_eye',
        'icon-ic_video1',
        'icon-ic_Money2',
        'icon-ic_gold1',
        'icon-ic_crown1',
        'icon-ic_box',
        'icon-ic_',
        'icon-ic_badge11',
        'icon-a-ic_Money111',
        'icon-ic_copy',
        'icon-ic_kanjia',
        'icon-ic_fire',
        'icon-ic_Like',
        'icon-ic_Eyes',
        'icon-ic_location4',
        'icon-ic_money',
        'icon-a-ic_QRcode',
        'icon-a-ic_setup1',
        'icon-ic_message3',
        'icon-ic_Disable',
        'icon-ic_up2',
        'icon-ic_down2',
        'icon-ic_right2',
        'icon-ic_left2',
        'icon-a-ic_discount1',
        'icon-a-ic_customerservice1',
        'icon-ic_horn1',
        'icon-a-ic_Imageandtextsorting',
        'icon-ic_badge2',
        'icon-ic_file',
        'icon-ic_gold',
        'icon-ic_invite',
        'icon-ic_member1',
        'icon-ic_crown',
        'icon-ic_sort',
        'icon-ic_gift1',
        'icon-ic_badge',
        'icon-ic_Refresh',
        'icon-a-ic_Picturearrangement',
        'icon-ic_member',
        'icon-ic_learn1',
        'icon-a-ic_ic_orderforgoods1',
        'icon-ic_badge1',
        'icon-ic_location1',
        'icon-ic_returnofgoods',
        'icon-ic_returnmoney',
        'icon-ic_phone',
        'icon-ic_QRcode',
        'icon-ic_location',
        'icon-ic_mall',
        'icon-a-ic_user1',
        'icon-ic_search',
        'icon-ic_delete',
        'icon-ic_camera',
        'icon-ic_edit',
        'icon-ic_ShoppingCart1',
        'icon-a-ic_CompleteSelect',
        'icon-ic_unselect',
        'icon-ic_message',
        'icon-ic_star',
        'icon-ic_star1',
        'icon-ic_close1',
        'icon-ic-complete1',
        'icon-ic_enlarge',
        'icon-ic_Reduce',
        'icon-ic_increase',
        'icon-ic_close',
        'icon-ic_complete',
        'icon-ic_clock',
        'icon-ic_user',
        'icon-ic_sale',
        'icon-ic_collect',
        'icon-ic_ShoppingCart',
        'icon-ic_customerservice',
        'icon-ic_rightarrow',
        'icon-ic_downarrow',
        'icon-ic_uparrow',
        'icon-ic_leftarrow'
      ]
    }
  },
  computed: {
    filteredIcons() {
      if (!this.search) return this.icons
      return this.icons.filter(icon => icon.toLowerCase().includes(this.search.toLowerCase()))
    }
  },
  methods: {
    copy(icon) {
      // #ifdef H5
      if (navigator && navigator.clipboard) {
        navigator.clipboard.writeText(icon).then(() => {
          uni.showToast({ title: '复制成功', icon: 'success' })
        })
      } else {
        // 兼容不支持clipboard的H5
        const textarea = document.createElement('textarea')
        textarea.value = icon
        document.body.appendChild(textarea)
        textarea.select()
        document.execCommand('copy')
        document.body.removeChild(textarea)
        uni.showToast({ title: '复制成功', icon: 'success' })
      }
      // #endif
      // #ifndef H5
      uni.setClipboardData({
        data: icon,
        success: () => {
          uni.showToast({ title: '复制成功', icon: 'success' })
        }
      })
      // #endif
    }
  }
}
</script>

<style lang="scss">
@import '@/static/iconfont/iconfont.css';
.icon-search {
  width: 100%;
  margin: 20rpx 0;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  box-sizing: border-box;
  background: #fafbfc;
  height: 80rpx;
  line-height: 80rpx;
}
.icon-demo {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  justify-content: center;
  gap: 20rpx;
}
.icon-wrap {
  height: 100rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
  margin-bottom: 20px;
}
.iconfont {
  font-size: 32px;
  margin-bottom: 8px;
}
.icon-name {
  font-size: 12px;
  color: #666;
  word-break: break-all;
  text-align: center;
}
.copy-btn {
  margin-top: 8rpx;
  font-size: 24rpx;
  padding: 0 16rpx;
  height: 48rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  color: #333;
}
</style>
