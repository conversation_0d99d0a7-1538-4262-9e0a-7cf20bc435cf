// addGoodsFormData
// singleSpecification -- 单规格
// attrValue -- 多规格数据
// modifyPriceData -- 多规格选择时，已经存储的数据
.container {
	padding-top: 20rpx;
	padding-bottom: 156rpx;
}

.tip {
	padding: 16rpx 0 0 40rpx;
	font-size: 22rpx;
	color: #e93323;
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	.iconfont {
		display: inline-block;
		margin-right: 10rpx;
	}
}

.popup_group {
	background: #fff;
	margin: 31rpx auto;
	width: 710rpx;
	font-size: 30rpx;
	border-radius: 10rpx;
	.popup_group_item {
		padding: 32rpx 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.popup_group_item_value {
			display: flex;
			align-items: center;
			.popup_group_item_message {
				margin-right: 15rpx;
				color: #bbbbbb;
				max-width: 400rpx;
				&_value {
					display: inline-block;
					max-width: 400rpx;
					color: #000;
				}
			}
			.iconfont {
				color: #bbbbbb;
			}
			input {
				text-align: right;
			}
		}
	}
	> view:not(:last-child) {
		border-bottom: 1px solid #eeeeee;
	}
}

.handle {
	width: 100%;
	height: 126rpx;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	position: fixed;
	left: 0;
	bottom: 0;
	z-index: 10;
	&_button {
		width: 690rpx;
		height: 86rpx;
		background: #e93323;
		border-radius: 43px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		color: #ffffff;
	}
}

.input_content {
	background: #fff;
	padding: 20rpx 30rpx 40rpx 30rpx;
	width: 710rpx;
	margin: auto;
	box-sizing: border-box;
	border-radius: 10rpx;
	&_textarea {
		border-bottom: 1px solid #eeeeee;
		padding-bottom: 19rpx;
		textarea {
			height: 114rpx;
		}
		> view {
			text-align: right;
			color: #666666;
			font-size: 24rpx;
		}
	}

	&_photo {
		margin-top: 30rpx;
		display: flex;
		flex-wrap: wrap;
		
		&_adPh {
			position: relative;
			width: 210rpx;
			height: 210rpx;
			border: 1px solid #dddddd;
			display: flex;
			flex-direction: column;
			justify-content: center;
			border-radius: 8rpx;
			margin-right: 10rpx;
			margin-bottom: 10rpx;
			box-sizing: border-box;
			&:nth-child(3n){
				margin-right: 0;
			}
			> view:nth-child(1) {
				height: 37rpx;
				margin-bottom: 16rpx;
				display: flex;
				justify-content: center;
				image {
					width: 45rpx;
					display: block;
				}
			}

			> view:nth-child(2) {
				text-align: center;
				color: #bbbbbb;
				font-size: 24rpx;
			}
			.photos {
				width: 206rpx;
				// height: 208rpx;
			}
			&_jiao {
				position: absolute;
				top: -2rpx;
				right: -1rpx;
				width: 40rpx;
				height: 40rpx;
				background: rgba(0,0,0,.6);
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 0 8rpx 0 8rpx;
				image {
					width: 16rpx;
					height: 16rpx;
				}
			}
		}
	}
	&_describe {
		border-top: 1px solid #eeeeee;
		padding-top: 30rpx;
		padding-bottom: 47rpx;
		border-bottom: 1px solid #eeeeee;
		&_title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			&_msg {
				color: #333333;
				font-size: 30rpx;
			}
			&_num {
				color: #666666;
				font-size: 24rpx;
			}
		}

		&_textarea {
			border-radius: 10px;
			margin-top: 20rpx;
			height: 180rpx;
			background: #f5f5f5;
			padding: 20rpx;
			textarea {
				font-size: 28rpx;
				height: 150rpx;
			}
		}
	}

	&_keyword {
		padding-top: 32rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 30rpx;
		&_value {
			flex: 1;
			margin-left: 30rpx;
			input {
				width: 100%;
				text-align: right;
			}
		}
	}
}
.photo_count{
	margin-top: 28rpx;
	.photo_size{
		font-size: 24rpx;
		color: #999999;
	}
}
.radio {
	padding: 30rpx;
	&_label {
		padding-left: 10rpx;
		color: #333333;
		font-size: 30rpx;
		display: flex;
		align-items: center;
		.select_check {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 40rpx;
			height: 40rpx;
			border: 1px solid #cccccc;
			border-radius: 50%;
			margin-right: 20rpx;

			.iconfont {
				font-size: 24rpx;
			}
		}

		.select {
			background: #e93323;
			border: none;
			.iconfont {
				color: #fff;
			}
		}
	}
	.select_group {
		display: flex;
	}
	.flex_start {
		padding: 0 10rpx;
		margin-top: 40rpx;
		justify-content: flex-start;
	}

	&_select {
		display: flex;
		margin-right: 110rpx;
	}
}

.dobuButton {
	padding: 0 30rpx;
}

.margin_right {
	background: #FD6523;
	margin-right: 30rpx;
}