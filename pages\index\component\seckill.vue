<template>
	<view v-if="spikeList.length > 0" class="skeleton-rect" :style="'padding-top:'+mbConfig+'rpx'">
		<view class="seckill-count" :class="'wrapper-count'+styleType" :style="'background:'+bgColor+';border-radius:'+bgStyle+'rpx'">
			<view class="spike-bd">
				<view class="acea-row row-middle">
					<image class="title-img" :src="`${domain}/static/images/spike_title.png`"></image>
					<view class="spike-distance">
						<text :style="'background-color:'+countDownColor" class="text bg-red">距结束</text>
						<countDown class="spike-count" :is-day="false" :tip-text="' '" :day-text="' '" :hour-text="':'" :minute-text="':'" :second-text="' '" :datatime="datatime" :colors="color" @getProduct="getProduct"></countDown>
					</view>
				</view>
				<navigator v-if="!merId" url="/pages/activity/goods_seckill/index" class="more-btn" hover-class="none">
					更多惊喜
					<text class="iconfont icon-ic_rightarrow" hover-class="none"></text>
				</navigator>
			</view>
			<view class="spike-wrapper" :class="'wrapper'+styleType">
				<scroll-view v-if="styleType != 2"  :class="'colum'+styleType" :scroll-x="styleType == 0 ? true : false" show-scrollbar="false">
					<navigator
						class="spike-item"
						v-for="(item, index) in spikeList"
						:key="index"
						:url="'/pages/activity/goods_seckill_details/index?id=' + item.product_id + '&time=' + item.stop + ''"
						hover-class="none">
						<view class="img-box" :class="'img-box'+conStyle">
							<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
						</view>
						<view class="info">
							<view v-if="titleShow" class="name line1">{{ item.store_name }}</view>
							<view v-if="progressShow" class="stock-box" :style="{ borderColor: themeColor, color: themeColor }">
								<view class="grabbed" :style="'width:' + item.percent + ';'"></view>
								<text class="stock-sales">{{ item.percent }}</text>
							</view>
							<view class="price-box">
								<view class="price" :style="{ color: themeColor }">
									<priceFormat :price="item.price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
								</view>
								<text v-if="priceShow" class="old-price regular">
									<text>¥</text>
									{{ item.ot_price }}
								</text>
							</view>
						</view>
					</navigator>
				</scroll-view>
				<block v-else class="acea-row row-between-wrapper combination">
					<navigator 
						class="combination-item" 
						v-for="(item, index) in spikeList" 
						:key="index"
						hover-class="none"
						:style="{ 'background-image': `url(${domain}/static/images/combination${index+1}.png)`,'border-radius':`${conStyle}rpx`}"
						:url="'/pages/activity/goods_seckill_details/index?id=' + item.product_id + '&time=' + item.stop + ''"
						>
						<view class="info">
							<view class="price-box combination-price">
								<view v-if="titleShow" class="name line1">{{ item.store_name }}</view>
								<view class="price" :style="{ color: themeColor }">
									<priceFormat :price="item.price" weight intSize="28" floatSize="20" labelSize="20"></priceFormat>
								</view>
								<text class="gocom_btn">
									去抢购
									<text class="iconfont icon-ic_rightarrow"></text>
								</text>
							</view>
						</view>
						<view class="img-box" :class="'img-box'+conStyles">
							<easy-loadimage mode="widthFix" :image-src="item.image"></easy-loadimage>
						</view>
					</navigator>
				</block>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	
	import { HTTP_REQUEST_URL } from '@/config/app';
	import countDown from '@/components/countDown';
	import easyLoadimage from '@/components/easy-loadimage/easy-loadimage.vue';
	import {
		getSeckillData
	} from '@/api/api.js';
	export default {
		name: 'seckill',
		components:{
			countDown,
			easyLoadimage
		},
		props: {
			dataConfig: {
				type: Object,
				default: () => {}
			},
			merId: {
				type: String || Number,
				default: ''
			}
		},
		data() {
			return {
				domain: HTTP_REQUEST_URL,
				datatime:0,
				color: "#E93323",
				spikeList: [],
				countDownColor: this.dataConfig.countDownColor.color[0].item,
				themeColor: this.dataConfig.themeColor.color[0].item,
				styleType: this.dataConfig.tabConfig.tabVal, //单行，多行，板块
				mbConfig: this.dataConfig.mbConfig.val*2,
				bgStyle: this.dataConfig.bgStyle.type ? 24 : 0,
				bgColor: this.dataConfig.bgColor.color[0].item,
				conStyle: this.dataConfig.conStyle.type ? 16 : 0,
				conStyles: this.dataConfig.conStyle.type,
				priceShow: this.dataConfig.priceShow.val,
				progressShow: this.dataConfig.progressShow.val,
				titleShow: this.dataConfig.titleShow.val,
				diy_id: this.dataConfig.did,
				unique: this.dataConfig.timestamp,
			};
		},
		created() {},
		mounted() {
			this.getSeckillData();
		},
		methods: {
			getSeckillData() {
				let that = this;
				getSeckillData({
					diy_id: that.diy_id,
					unique: that.unique,
					mer_id: that.merId,
					limit: that.styleType == 2 ? 3 : 12
				}).then(res => {
					  that.datatime = res.data&&res.data.stop;
						if(res.data&&res.data.list.length >= 3){
							that.spikeList = res.data.list
						}else{
							if(res.data&&res.data.list.length > 0 && that.styleType == 2){
								let data = res.data.list
								that.spikeList = data.length == 1 ? [data[0],data[0],data[0]] : [data[0],data[1],data[1]]
							}else{
								that.spikeList = res.data&&res.data.list || []
							}
						}
						that.spikeList.map(item => {
							item.percent = item.stock === 0 ? '100%' : (item.sales*100 / (item.sales+item.stock)).toFixed(2) + '%';
						});
				}).catch(e => {});
			},
			getProduct(){
				this.getSeckillData();
			}
		}
	}
</script>

<style lang="scss" scoped>
	@import '../style/main.scss';
	.seckill-count {
		background-color: #fff;
		margin: 0 20rpx;
		border-radius: 16rpx;
		padding: 24rpx 0 0 20rpx;
		box-shadow: 4rpx 2rpx 12rpx 2rpx rgba(0, 0, 0, 0.03);
		&.wrapper-count2{
			padding: 24rpx 20rpx 26rpx;
		}
	}
	.spike-count{
		display: flex;
		justify-content: center;
		align-items: center;
	}
	/deep/.spike-bd .red{
		color: #e93323!important;
	}
	.spike-distance {
		margin-left: 15rpx;
		position: relative;
		top: 1.4rpx;
		display: flex;
		border: 1px solid #e93323;
		border-radius: 8rpx;
		height: 40rpx;
		padding-left: 80rpx;
		.bg-red {
			font-size: 20rpx;
			color: #fff;
			background-color: #e93323;
			padding: 0 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			height: 100%;
			position: absolute;
			left: 0;
			border-radius: 4rpx 0 0 4rpx;
		}
		.time {
			font-size: 22rpx;
			padding: 0 12rpx;
			color: #e93323;
			/deep/.red {
				margin: 0;
			}
		}
		.red-color {
			color: #e93323;
		}
	}
	.stock-box {
		width: 100%;
		height: 20rpx;
		background-color: #ffdcd9;
		border-radius: 20rpx;
		margin-top: 13rpx;
		position: relative;
		color: #fff;
		font-size: 18rpx;
		line-height: 20rpx;
		text-align: center;
		overflow: hidden;
		.grabbed {
			height: 20rpx;
			background: linear-gradient(#ff0000, #ff5400);
			position: absolute;
			top: 0;
			left: 0;
			border-radius: 20rpx;
		}
		.stock-sales {
			position: absolute;
			left: 0;
			width: 100%;
			text-align: center;
			color: #ffffff;
		}
	}
</style>
