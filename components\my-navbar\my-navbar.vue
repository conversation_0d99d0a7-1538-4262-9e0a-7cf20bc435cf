<template>
  <view class="top">
    <!-- #ifdef MP || APP-PLUS -->
    <view class="sys-head">
      <view class="sys-bar" :style="{ height: sysHeight }"></view>
      <!-- #ifdef MP -->
      <view class="sys-body">
        <view class="icon-arrow center" @click="handBak">
          <text v-if="isHome" class="iconfont icon-ic_home"></text>
          <text v-else class="iconfont icon-ic_leftarrow"></text>
        </view>
        <view class="sys-title">{{ title }}</view>
        <view class=""><slot></slot></view>
      </view>
      <!-- #endif -->
      <view class="bg"></view>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  name: 'my-navbar',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sysHeight: '',
      isHome: false //是否返回首页
    }
  },
  created() {
    this.sysHeight = uni.getSystemInfoSync().statusBarHeight + 'px'
    let routes = getCurrentPages()
    this.isHome = routes.length <= 1
  },
  methods: {
    handBak() {
      console.log('000')
      if (this.isHome) {
        uni.switchTab({ url: '/pages/index/index' })
      } else {
        uni.navigateBack()
      }
    }
  }
}
</script>

<style lang="scss">
.top {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
}
.sys-head {
}
.sys-body {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 28rpx;
  position: relative;
  .icon-arrow {
		width: 86rpx;
    height: 86rpx;
    position: absolute;
    left: 28rpx;
    top: 0;
    z-index: 20;
    .iconfont {
      font-size: 38rpx;
      color: white;
    }
  }
  .sys-title {
    flex: 1;
    text-align: center;
  }
}
</style>
