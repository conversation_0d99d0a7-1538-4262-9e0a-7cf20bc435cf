<template>
	<view :style="viewColor">
		<!-- 选择送货方式 -->
		<view class="mask-box">
			<view class='mask' catchtouchmove="true" :hidden='isShowBox==false' @tap='closeShowBox'></view>
			<view class="mask-content animated popup-main bg-f" :class="{slideInUp:isShowBox}">
				<view class="title font-500">
					配送方式
					<view class="close iconfont icon-ic_close popup-close" @click="closeShowBox"></view>
				</view>
				<view class="box">
					<view class="check-item" v-for="(item,index) in radioList" :key="index" :class="{on:index == radioIndex}">
						<view>{{item.title}}</view>
						<view class="radio" @click="bindCheck(item,index)"> 
							<block v-if="index == newData.order.isTake">
								<view class="iconfont icon-a-ic_CompleteSelect"></view>
							</block>
							<block v-else>
								<view class="iconfont icon-ic_unselect"></view>
							</block>
						</view>
					</view>
				</view>
				<view class="foot">
					<view class="btn" @click="confirmBtn">确定</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------
	import { mapGetters } from "vuex";
	export default{
		name:'checkDelivery',
		props:{
			isShowBox:{
				type:Boolean,
				default:false
			},
			activeObj:{
				type:Object,
				default:function(){
					return {}
				}
			},
			deliveryName:{
				type:String,
				default:'快递配送'
			},
			radioList:{
				type:Array,
				default: [
					{
						title:'快递配送',
						check:true
					},
					{
						title:'到店核销',
						check:false
					}
				],
			},
		},
		computed: mapGetters(['viewColor']),
		data(){
			return {
				radioIndex:0,
				oldRadioIndex:'', //旧的索引
				newData:{}
			}
		},
		created() {
			this.newData = JSON.parse(JSON.stringify(this.activeObj))
		},
		methods:{
			// 关闭配送方式弹窗
			closeShowBox(){
				this.$emit('close')
			},
			// 选择配送方式
			bindCheck(item,index){
				this.newData.order.isTake = index
			},
			confirmBtn(){
				this.$emit('confirmBtn',this.newData)
			}
		}
	}
</script>

<style lang="scss">
	.mask-box{
		.mask-content{
			.title{
				padding: 40rpx 0;
				margin-bottom: 40rpx;
				.close{
					position: absolute;
					right: 20rpx;
					top: 20rpx;	
				}
			}
			.box{
				padding: 0 30rpx;
				.check-item{
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 40rpx;
					margin-bottom: 50rpx;
					font-size: 28rpx;
					.iconfont{
						font-size: 38rpx;
						color: #CCCCCC;
						&.icon-a-ic_CompleteSelect{
							color: var(--view-theme);
						}
					}
				}
			}
			.foot{
				padding: 50rpx 30rpx 20rpx;
				padding: 50rpx 30rpx calc(20rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
				padding: 50rpx 30rpx calc(20rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
				.btn{
					width: 100%;
					height: 88rpx;
					line-height: 88rpx;
					text-align: center;
					border-radius: 50rpx;
					color: #fff;
					font-size: 28rpx;
					background: var(--view-theme);
				}
			}
		}	
	}
	.animated {
		animation-duration: .3s
	}
</style>
