<template>
  <view class="role-tag center" :class="role === '店长' ? 'role-1' : 'role-2'">
    <image
      :src="role === '店长' ? '/static/images/role-admin.png' : '/static/images/role-other.png'"
      mode="widthFix"
      class="role-img"
    ></image>
    {{ role }}
  </view>
</template>

<script>
export default {
  name: 'roleTags',
  props: { role: { type: String, default: '总监' } },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.role-tag {
  width: 68rpx;
  height: 28rpx;
  background: linear-gradient(48deg, #ffffff, #434343, #7c7c7c, #434343);
  border-radius: 6rpx;
  font-weight: 500;
  font-size: 18rpx;
  color: #fff4e0;
  margin: 0 10rpx;
}
.role-1 {
  font-weight: 500;
  font-size: 18rpx;
  color: #fff4e0;
}
.role-2 {
  font-weight: 500;
  font-size: 18rpx;
  color: #ffffff;
}
.role-img {
  width: 14rpx;
  height: 15rpx;
  margin-right: 6rpx;
}
</style>
