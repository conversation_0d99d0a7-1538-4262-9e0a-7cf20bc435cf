---
description: 
globs: 
alwaysApply: false
---
# Components

本规则文件描述了 `[components/](mdc:components)` 目录的结构和一些关键组件。

## 目录结构

`components` 目录包含了项目中所有可复用的Vue组件。遵循着两种主要的组织方式：

1.  **单个文件组件**: 对于简单的组件，会直接在 `components` 根目录下创建一个 `.vue` 文件，例如 `[customTab.vue](mdc:components/customTab.vue)`。
2.  **目录组件**: 对于较复杂的组件，会创建一个与组件同名的目录，其中包含 `index.vue` 文件以及其他相关资源（如图片、样式或子组件）。例如 `[productWindow/](mdc:components/productWindow)`。

## 关键组件示例

以下是一些项目中常用或比较核心的组件：

-   `[customTab/](mdc:components/customTab)`: 全局自定义底部导航栏。
-   `[productWindow/](mdc:components/productWindow)`: 商品规格选择弹窗，常用于商品详情页和购物车。
-   `[addressWindow/](mdc:components/addressWindow)`: 地址选择弹窗。
-   `[payment/](mdc:components/payment)`: 支付方式选择组件，集成了多种支付方式。
-   `[WaterfallsFlow/](mdc:components/WaterfallsFlow)`: 瀑布流布局组件，用于商品列表等场景。
-   `[jyf-parser/](mdc:components/jyf-parser)`: 富文本解析器，用于渲染后端返回的HTML内容。
-   `[verify/](mdc:components/verify)`: 安全验证组件，例如滑块验证码。

当需要实现新功能时，可以优先检查此目录中是否有可复用的组件。

