<template>
  <view class="page-container">
    <view class="reactive">
      <view class="user-info">
        <image :src="userInfo.avatar" class="avatar" mode="widthFix" />
        <view>
          <view class="nickname">{{ userInfo.nickname }}</view>
          <view class="uid">{{ userInfo.uid }}</view>
        </view>
      </view>
      <!-- 评价列表 -->
      <block v-if="replyList.length > 0">
        <block v-for="item in replyList" :key="item.id">
          <view class="store-detail-wrap">
            <view class="store-detail__comment-head"></view>
            <view class="store-detail__comment-item">
              <!-- <image :src="item.avatar" class="store-detail__comment-avatar"></image> -->
              <view class="store-detail__comment-content">
                <view class="store-detail__comment-header">
                  <view class="store-detail__comment-user">
                    <view class="user">
                      {{ item.mer_name }}
                      <text class="iconfont icon-ic_rightarrow"></text>
                    </view>
                    <view class="id">
                      发布时间: {{ item.create_time }}
                      <view class="status">{{ getStatus(item.status) }}</view>
                    </view>
                  </view>
                  <view class="store-detail__comment-satisfy">
                    <Expression :score="item.product_score" />
                  </view>
                </view>
                <view class="store-detail__comment-tags">
                  <block v-for="(tag, index) of item.tagsList" :key="index">
                    <text class="store-detail__comment-tag">{{ tag }}</text>
                  </block>
                </view>
                <view class="store-detail__comment-text">
                  {{ item.comment }}
                </view>
                <view class="store-detail__comment-footer">
                  <view class="store-detail__comment-name">
                    <view class="tag center">
                      <image src="/static/images/icon-user.png" mode="widthFix" class="img"></image>
                    </view>
                    {{ item.service_name }}
                  </view>
                  <view class="store-detail__comment-time">{{ item.create_time }}</view>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 加载状态 -->
        <view class="load-status">
          <view v-if="loading" class="loading-text">加载中...</view>
          <view v-else-if="!hasMore && replyList.length > 0" class="no-more-text">
            没有更多数据了
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view v-else-if="!loading" class="empty-state">
        <view class="empty-text">暂无评价数据</view>
      </view>
    </view>
  </view>
</template>

<script>
import { getUserInfo } from '@/api/user.js'
import { getMyGoodsComment } from '@/api/hairdressing.js'
import Expression from '@/components/expression/expression'
export default {
  data() {
    return {
      userInfo: {},
      // 评价列表
      replyList: [],
      // 分页相关
      page: 1,
      limit: 10,
      loading: false,
      hasMore: true,
    }
  },
  components: {
    Expression,
  },
  onLoad(options) {
    this.loadUserInfo()
    this.loadMyGoodsComment()
  },
  onReachBottom() {
    // 触底加载更多
    if (this.hasMore && !this.loading) {
      this.loadMore()
    }
    // 停止下拉刷新
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 20000)
  },
  onPullDownRefresh() {
    // 下拉刷新
    this.refreshData()
  },
  methods: {
    loadUserInfo() {
      getUserInfo().then((res) => {
        this.userInfo = res.data
      })
    },
    loadMyGoodsComment() {
      if (this.loading) return
      this.loading = true
      getMyGoodsComment({
        page: this.page,
        limit: this.limit,
      })
        .then((res) => {
          const list = res.data.list || []
          list.map((item) => {
            item.tagsList = item.tags.split(',')
          })
          const loadend = list.length < this.limit
          this.hasMore = !loadend
          this.replyList = [...this.replyList, ...list]
        })
        .catch((err) => {
          this.$util.Tips({ title: '加载失败' })
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 加载更多
    loadMore() {
      if (!this.hasMore || this.loading) return
      this.page++
      this.loadMyGoodsComment()
    },

    // 刷新数据
    refreshData() {
      this.page = 1
      this.hasMore = true
      this.replyList = []
      this.loadMyGoodsComment()
    },
    getStatus(status) {
      switch (status) {
        case 0:
          return '审核中'
        case 1:
          return '已发布'
        case -1:
          return '未通过'
        default:
          return ''
      }
    },
  },
}
</script>

<style>
page {
  background-color: white;
}
</style>

<style lang="scss" scoped>
.page-container {
  position: relative;
  padding: 30rpx;
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 200rpx;
    background: linear-gradient(180deg, #fde7d1 0%, #ffffff 100%);
  }
}
.reactive {
  position: relative;
  z-index: 9;
}
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 80rpx;
  .avatar {
    width: 98rpx;
    height: 98rpx;
    margin-right: 20rpx;
    border-radius: 50%;
  }
  .nickname {
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    margin-bottom: 10rpx;
  }
  .uid {
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
  }
}
.store-detail-wrap {
  .store-detail__comment-item {
    display: flex;
    align-items: flex-start;
    margin-top: 10rpx;

    .store-detail__comment-content {
      flex: 1;
      .store-detail__comment-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .user {
          color: #333333;
          margin-bottom: 10rpx;
          font-size: 28rpx;
          color: #333333;
          display: flex;
          align-items: center;
          font-weight: bold;
          .iconfont {
            font-weight: bold;
          }
        }
        .id {
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
        }
        .status {
          font-weight: 400;
          font-size: 20rpx;
          color: #c9a063;
          border-radius: 8rpx;
          border: 1px solid #c9a063;
          display: inline-block;
          margin-left: 20rpx;
        }
      }

      .store-detail__comment-tags {
        display: flex;
        align-items: center;
        column-gap: 20rpx;
        margin: 20rpx 0;
        .store-detail__comment-tag {
          background: #fafafa;
          border-radius: 20rpx;
          padding: 4rpx 12rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
      }
      .store-detail__comment-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #666666;
        margin-bottom: 20rpx;
      }
      .store-detail__comment-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .store-detail__comment-name {
          display: flex;
          align-items: center;
          column-gap: 8rpx;
          .tag {
            width: 23rpx;
            height: 23rpx;
            background: #c9a063;
            border-radius: 4rpx;
            .img {
              width: 12rpx;
              height: 16rpx;
            }
          }
          background: #fafafa;
          border-radius: 8rpx;
          padding: 10rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
        .store-detail__comment-time {
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
      }
    }
  }
}

.load-status {
  text-align: center;
  padding: 40rpx 0;
  .loading-text,
  .no-more-text {
    font-size: 24rpx;
    color: #999;
  }
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
