<template>
  <uni-popup ref="popup" type="bottom" :mask="true" @close="closePopup" @change="changePopup">
    <view class="login-popup">
      <view class="popup-content">
        <!-- 标题栏 -->
        <view class="popup-header">
          <text class="title">获取您的头像/昵称</text>
          <text class="close-btn iconfont icon-ic_close" @tap="closePopup"></text>
        </view>

        <!-- 头像选择区域 -->
        <view class="avatar-section">
          <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
            <image :src="avatarUrl" class="avatar-img"></image>
            <view class="icon center">
              <text class="iconfont icon-ic_camera2"></text>
            </view>
          </button>
          <text class="label">头像</text>
        </view>

        <!-- 昵称输入区域 -->
        <view class="nickname-section">
          <text class="label">昵称</text>
          <input
            type="nickname"
            class="nickname-input"
            placeholder="一键使用您的微信昵称"
            placeholder-class="input-placeholder"
            v-model="nickName"
            @change="onInputNickname"
          />
        </view>

        <!-- 保存按钮 -->
        <button class="save-btn" hover-class="button-hover" @tap="handleSave">授权登录</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
const app = getApp()
import Routine from '@/libs/routine'
import Cache from '@/utils/cache'
import { commonAuth } from '@/api/public'
import { EXPIRES_TIME, USER_INFO } from '@/config/cache'
import { editAvatar } from '@/api/user.js'

export default {
  name: 'loginPopup',
  data() {
    return {
      avatarUrl: '/static/images/default-avatar.png', // 默认头像
      nickName: '',
      code: '',
    }
  },

  methods: {
    changePopup(e) {
      if (e.show) {
        this.getCode()
      }
    },
    getCode() {
      let that = this
      // #ifdef MP
      Routine.getCode()
        .then((code) => {
          uni.hideLoading()
          that.code = code
        })
        .catch((e) => {
          uni.hideLoading()
          uni.showToast({
            title: '登录失败',
            duration: 2000,
          })
        })
      // #endif
      // #ifndef MP
      that.code = 1
      // #endif
    },
    closePopup() {
      this.$refs.popup.close()
      this.$emit('close')
    },
    // 关闭弹窗并加载父层用户信息
    closeAttr() {
      this.closePopup()
      this.$emit('closeAttr')
    },
    // 打开弹窗
    openPopup() {
      this.$refs.popup.open()
    },
    // 微信头像获取
    onChooseAvatar(e) {
      console.log('🚀 ~ onChooseAvatar ~ e:', e)
      const { avatarUrl } = e.detail
      this.$util.uploadImgs(
        'upload/image',
        avatarUrl,
        (res) => {
          console.log('🚀 ~ onChooseAvatar ~ res:', res)
          this.avatarUrl = res.data.path
        },
        (err) => {
          console.log(err)
        },
      )
    },
    onInputNickname(e) {
      this.nickName = e.detail.value
    },
    handleSave() {
      if (!this.avatarUrl || !this.nickName) {
        uni.showToast({
          title: '请完善头像和昵称',
          icon: 'none',
        })
        return
      }
      this.getAuthLogin()
      //   this.$emit('confirm', {
      //     avatarUrl: this.avatarUrl,
      //     nickName: this.nickName,
      //   })
      //   this.closePopup()
    },
    getAuthLogin() {
      let self = this
      uni.showLoading({
        title: '正在登录中',
      })
      Routine.getUserProfile()
        .then((res) => {
          console.log('🚀 ~ .then ~ res:', res)
          let userInfo = res.userInfo
          userInfo.code = self.code
          userInfo.spread = app.globalData.spid //获取推广人ID
          userInfo.spread_code = app.globalData.code //获取推广人分享二维码ID
          userInfo.userInfo.nickName = self.nickName || '会员用户'
          userInfo.userInfo.avatarUrl = self.avatarUrl
          commonAuth({
            auth: {
              type: 'routine',
              auth: userInfo,
            },
          })
            .then((res) => {
              if (res.data.status == 200) {
                let time = res.data.result.expires_time - Cache.time()
                self.$store.commit('UPDATE_USERINFO', res.data.result.user)
                self.$store.commit('LOGIN', { token: res.data.result.token, time: time })
                self.$store.commit('SETUID', res.data.result.user.uid)
                Cache.set(EXPIRES_TIME, res.data.result.expires_time, time)
                Cache.set(USER_INFO, res.data.result.user, time)
                console.log(self.preFullPage)
                if (res.data.result.user.isNew && self.mp_is_new && self.first_avatar_switch == 1) {
                  uni.hideLoading()
                  self.isShow = true
                } else {
                  const userInfo = {
                    avatar: self.avatarUrl,
                    nickname: self.nickName,
                  }
                  editAvatar(userInfo).then((res) => {
                    self.$util.Tips(
                      {
                        title: '登录成功',
                        icon: 'success',
                      },
                      () => {
                        self.closeAttr()
                      },
                    )
                  })
                }
              } else {
                uni.setStorageSync('auth_token', res.data.result.key)
                return uni.navigateTo({
                  url: '/pages/users/login/index',
                })
              }
            })
            .catch((res) => {
              uni.hideLoading()
              console.log('auth' + res)
              uni.showToast({
                title: res,
                icon: 'none',
                duration: 2000,
              })
            })
        })
        .catch((res) => {
          uni.hideLoading()
          console.log('Routine' + res)
          uni.showToast({
            title: res.errMsg,
            icon: 'none',
            duration: 2000,
          })
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.login-popup {
  width: 100%;
  .popup-content {
    background-color: #fff;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    padding: 40rpx 30rpx 220rpx;
    .popup-header {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      padding-bottom: 30rpx;

      .title {
        font-size: 32rpx;
        font-weight: 500;
      }

      .close-btn {
        position: absolute;
        right: 0;
        top: 0;
        font-size: 40rpx;
        color: #cccccc;
      }
    }

    .avatar-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 30rpx 0;

      .avatar-wrapper {
        width: 120rpx;
        height: 120rpx;
        padding: 0;
        margin: 0;
        background: none;

        &::after {
          border: none;
        }
        .icon {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 38rpx;
          height: 38rpx;
          background: #ffffff;
          box-shadow: 0rpx 3rpx 4rpx 0rpx rgba(0, 0, 0, 0.08);
          border-radius: 50%;
          .iconfont {
            font-size: 20rpx;
            color: #999999;
          }
        }
      }

      .avatar-img {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
      }

      .label {
        font-size: 28rpx;
        color: #333;
        margin-top: 20rpx;
      }
    }

    .nickname-section {
      display: flex;
      align-items: center;
      background: #fafafa;
      border-radius: 40rpx;
      padding: 0 40rpx;

      .label {
        display: block;
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
      }

      .nickname-input {
        flex: 1;
        height: 80rpx;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #999999;
      }

      .input-placeholder {
        color: #999;
      }
    }

    .save-btn {
      margin-top: 60rpx;
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      background-color: #c4a675;
      color: #fff;
      font-size: 32rpx;
      border-radius: 44rpx;

      &::after {
        border: none;
      }
    }
  }
}
</style>
