<template>
  <view class="server-page">
    <view class="comment-filter">
      <view class="comment-filter__tabs">
        <view class="comment-filter__tab comment-filter__tab--active">
          全部 {{ replyInfo.count }}
        </view>
        <view class="comment-filter__tab">有图 23</view>
        <view class="comment-filter__tab">满意 103</view>
        <view class="comment-filter__tab">一般 3</view>
        <view class="comment-filter__tab">不满意 0</view>
      </view>
      <view class="comment-filter__tags">
        <block v-for="(tag, index) of replyInfo.tagsList" :key="index">
          <view class="comment-filter__tag">{{ tag.name }} {{ tag.count }}</view>
        </block>
      </view>
    </view>
    <view class="store-detail__comments">
      <block v-for="item in replyInfo.list" :key="item.id">
        <view class="store-detail__comment-item">
          <image :src="item.avatar" class="store-detail__comment-avatar"></image>
          <view class="store-detail__comment-content">
            <view class="store-detail__comment-header">
              <view class="store-detail__comment-user">
                <view class="user">用户{{ item.nickname }}</view>
                <view class="id">ID: {{ item.uid }}</view>
              </view>
              <view class="store-detail__comment-satisfy">
                <Expression :score="item.product_score" />
              </view>
            </view>
            <view class="store-detail__comment-tags">
              <block v-for="(tag, index) of item.tagsList" :key="index">
                <text class="store-detail__comment-tag">{{ tag }}</text>
              </block>
            </view>
            <view class="store-detail__comment-text">
              {{ item.comment }}
            </view>
            <view class="store-detail__comment-footer">
              <view class="store-detail__comment-name">
                <view class="tag center">
                  <image src="/static/images/icon-user.png" mode="widthFix" class="img"></image>
                </view>
                {{ item.service_name }}
              </view>
              <view class="store-detail__comment-time">{{ item.create_time }}</view>
            </view>
          </view>
        </view>
      </block>
    </view>
  </view>
</template>

<script>
import Expression from '@/components/expression/expression.vue'
import { getReplyList } from '@/api/hairdressing.js'
export default {
  name: 'CommentFilter',
  components: { Expression },
  data() {
    return {
      mer_id: '',
      replyInfo: {},
    }
  },
  onLoad(options) {
    if (options.mer_id) {
      this.mer_id = options.mer_id
      this.loadReplyList()
    }
  },
  methods: {
    //加载评论列表
    loadReplyList() {
      getReplyList(this.mer_id, { page: 1, limit: 100 }).then((res) => {
        res.data.tagsList = Object.keys(res.data.tag_stats).map((item) => {
          return {
            name: item,
            count: res.data.tag_stats[item],
          }
        })
        res.data.list.forEach((item) => {
          if (item.tags && item.tags !== null) {
            console.log('🚀 ~ res.data.list.forEach ~ item:', item.tags)
            item.tagsList = item.tags.split(',')
          } else {
            item.tagsList = []
          }
        })
        this.replyInfo = res.data
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.server-page {
}
.comment-filter {
  margin-top: 10rpx;
  padding: 20rpx 30rpx;
  background: white;
  &__tabs {
    display: flex;
    align-items: center;
    column-gap: 20rpx;
  }
  &__tab {
    padding: 10rpx 18rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #d19e58;
    border-radius: 36rpx;
    border: 1px solid #d19e58;
    &--active {
      background: #d19e58;
      color: #ffffff;
    }
  }

  &__tags {
    display: flex;
    align-items: center;
    column-gap: 20rpx;
    margin-top: 20rpx;
  }

  &__tag {
    padding: 10rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #adadad;
    background: #fafafa;
    border-radius: 20rpx;
  }
}
.store-detail__comments {
  margin-top: 10rpx;
  background-color: white;
  padding: 40rpx 30rpx;
  .store-detail__comment-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 100rpx;
    &:last-child {
      margin-bottom: 0;
    }
    .store-detail__comment-avatar {
      width: 68rpx;
      height: 68rpx;
      border-radius: 50%;
      margin-right: 20rpx;
    }
    .store-detail__comment-content {
      flex: 1;
      .store-detail__comment-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .user {
          font-weight: 500;
          font-size: 24rpx;
          color: #333333;
          margin-bottom: 10rpx;
        }
        .id {
          font-weight: 400;
          font-size: 22rpx;
          color: #666666;
        }
      }
      .store-detail__comment-tags {
        display: flex;
        align-items: center;
        column-gap: 20rpx;
        margin: 20rpx 0;
        .store-detail__comment-tag {
          background: #fafafa;
          border-radius: 20rpx;
          padding: 4rpx 12rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
      }
      .store-detail__comment-text {
        font-weight: 400;
        font-size: 20rpx;
        color: #666666;
        margin-bottom: 20rpx;
      }
      .store-detail__comment-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .store-detail__comment-name {
          display: flex;
          align-items: center;
          column-gap: 8rpx;
          .tag {
            width: 23rpx;
            height: 23rpx;
            background: #c9a063;
            border-radius: 4rpx;
            .img {
              width: 12rpx;
              height: 16rpx;
            }
          }
          background: #fafafa;
          border-radius: 8rpx;
          padding: 10rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
        .store-detail__comment-time {
          font-weight: 400;
          font-size: 20rpx;
          color: #adadad;
        }
      }
    }
  }
}
</style>
