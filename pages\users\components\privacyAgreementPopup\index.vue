<template>
	<view :style="viewColor">
		<view class="mask" @touchmove.prevent :hidden="isShow == false" @click="rejectAgreement"></view>
		<view class="product-window popup-main bg-f" :class="{'on':isShow}">
			<view class="mp-data">
				<text class="mp-name font-bold">{{site_name}}服务与隐私协议</text>
			</view>
			<view class="trip-msg">
				<view class="trip">
					欢迎您使用{{site_name}}！请仔细阅读以下内容，并作出适当的选择：
				</view>
			</view>
			<view class="trip-title">
				隐私政策概要
			</view>
			<view class="trip-msg">
				<view class="trip">
					当您点击同意并开始时用产品服务时，即表示您已理解并同息该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法继续下一步操作。
				</view>
			</view>
			<view class="main-color" @click.stop="privacy(3)">点击阅读{{agreementName}}</view>
			<view class="bottom">
				<button class="save open" type="default" id="agree-btn" open-type="agreePrivacyAuthorization"
					@agreeprivacyauthorization="handleAgree">同意并继续</button>
				<button class="reject" @click="rejectAgreement">
					取消
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	// +----------------------------------------------------------------------
	// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
	// +----------------------------------------------------------------------
	// | Copyright (c) 2016~2024 https://www.crmeb.com All rights reserved.
	// +----------------------------------------------------------------------
	// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
	// +----------------------------------------------------------------------
	// | Author: CRMEB Team <<EMAIL>>
	// +----------------------------------------------------------------------	
	const app = getApp();
	import { mapGetters } from 'vuex';
	import { configMap } from "@/utils";
	import Routine from '@/libs/routine';
	export default {
		data() {
			return {
				isShow: false,
				agreementName: '',
				mpData: uni.getStorageSync('copyRight'),
			};
		},
		computed: {
			...configMap({site_name: ''}, mapGetters(['viewColor'])),
		},
		mounted() {
			wx.getPrivacySetting({
				success: res => {
					console.log(res.needAuthorization)
					if (res.needAuthorization) {
						// 需要弹出隐私协议
						this.isShow = true
						this.agreementName = res.privacyContractName
					} else {
						this.$emit('onAgree');
						// 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
					}
				},
				fail: () => {},
				complete: () => {}
			})
		},
		methods: {
			// 同意
			handleAgree() {
				this.isShow = false
				this.$emit('onclose');			
			},
			// 拒绝
			rejectAgreement() {
				this.isShow = false
				this.$emit('onReject');
			},
			// 跳转协议
			privacy(type) {
				uni.navigateTo({
					url: "/pages/users/privacy/index?type=" + type
				})
			},
		}
	}
</script>
<style scoped lang="scss">
	.pl-sty {
		color: #999999;
		font-size: 30rpx;
	}
	.product-window {
		padding: 64rpx 40rpx;
		padding-bottom: 38rpx;
		padding-bottom: calc(38rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
		padding-bottom: calc(38rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
		.mp-data {
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 40rpx;
			.mp-name {
				font-size: 34rpx;
				color: #333333;
				line-height: 48rpx;
			}
		}
		.trip-msg {
			padding-bottom: 32rpx;
			.title {
				font-size: 30rpx;
				font-weight: bold;
				color: #000;
				margin-bottom: 6rpx;
			}
			.trip {
				color: #333333;
				font-size: 28rpx;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
			}
		}
		.trip-title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333333;
			margin-bottom: 8rpx;
		}
		.main-color {
			font-size: 28rpx;
			font-weight: 400;
			color: var(--view-theme);
			margin-bottom: 40rpx;
		}
		.bottom {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-direction: column;
			.save,
			.reject {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 670rpx;
				height: 80rpx;
				border-radius: 80rpx;
				background-color: #F5F5F5;
				color: #333;
				font-size: 30rpx;
				font-weight: 500;
			}
			.save {
				background-color: var(--view-theme);
				color: #fff;
				margin-bottom: 24rpx;
			}
		}
	}
</style>