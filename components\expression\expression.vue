<template>
  <view class="expression">
    <text>{{ expression }}</text>
    <image :src="icon" mode="widthFix" class="icon"></image>
  </view>
</template>

<script>
export default {
  name: 'expression',
  props: {
    score: {
      type: Number,
      default: 5,
    },
  },
  computed: {
    expression() {
      if (this.score >= 3) {
        return '满意'
      } else if (this.score >= 1) {
        return '一般'
      } else {
        return '不满意'
      }
    },
    icon() {
      if (this.score >= 3) {
        return 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/326b7202508011746153413.png'
      } else if (this.score >= 1) {
        return 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/689ca202508011746159723.png'
      } else {
        return 'https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/a826c202508011746154152.png'
      }
    },
  },

  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.expression {
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  display: flex;
  align-items: center;
  column-gap: 8rpx;
}
.icon {
  width: 42rpx;
  height: 42rpx;
}
</style>
