<template>
  <view class="publish-work">
    <!-- 表单内容 -->
    <view class="form-container">
      <!-- 作品图片 -->
      <view class="form-section">
        <view class="section-title">
          <text>作品图片</text>
          <text class="required">*</text>
        </view>
        <view class="image-upload-area">
          <view class="upload-grid">
            <view v-for="(image, index) in publishForm.images" :key="index" class="image-item">
              <image :src="image" mode="aspectFill" @click="previewImage(index)"></image>
              <view class="remove-btn" @click="removeImage(index)">
                <text class="iconfont icon-ic_close"></text>
              </view>
            </view>
            <view
              v-if="publishForm.images.length < maxImages"
              class="upload-btn"
              @click="chooseImage"
            >
              <text class="iconfont icon-ic_camera"></text>
              <text class="upload-text">添加图片</text>
              <text class="upload-limit">{{ publishForm.images.length }}/{{ maxImages }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 作品标题 -->
      <view class="form-section">
        <view class="section-title">
          <text>作品标题</text>
          <text class="required">*</text>
        </view>
        <view class="input-wrapper">
          <input
            class="form-input"
            v-model="publishForm.title"
            placeholder="请输入作品标题"
            maxlength="50"
            :focus="titleFocus"
          />
          <view class="char-count">{{ publishForm.title.length }}/50</view>
        </view>
      </view>

      <!-- 作品标签 -->
      <view class="form-section">
        <view class="section-title">
          <text>作品标签</text>
        </view>
        <view class="tags-container">
          <view class="tags-input-wrapper">
            <input
              class="form-input"
              v-model="tagInput"
              placeholder="输入标签后按确定添加"
              @confirm="addTag"
              @input="onTagInput"
            />
          </view>
          <view class="tags-list" v-if="publishForm.tags.length > 0">
            <view v-for="(tag, index) in publishForm.tags" :key="index" class="tag-item">
              <text>{{ tag }}</text>
              <text class="remove-tag" @click="removeTag(index)">×</text>
            </view>
          </view>
          <view class="popular-tags">
            <text class="popular-title">热门标签：</text>
            <view
              v-for="tag in popularTags"
              :key="tag"
              class="popular-tag"
              @click="addPopularTag(tag)"
            >
              {{ tag }}
            </view>
          </view>
        </view>
      </view>

      <!-- 作品描述 -->
      <view class="form-section">
        <view class="section-title">
          <text>作品描述</text>
          <text class="required">*</text>
        </view>
        <view class="textarea-wrapper">
          <textarea
            class="form-textarea"
            v-model="publishForm.content"
            placeholder="请详细描述你的作品特色、适合人群、设计理念等..."
            maxlength="500"
            :auto-height="true"
            :show-confirm-bar="false"
          ></textarea>
          <view class="char-count">{{ publishForm.content.length }}/500</view>
        </view>
      </view>
    </view>
    <!-- 发布按钮 -->
    <view class="form-section">
      <button
        class="publish-button"
        :disabled="!canPublish"
        @click="publishWork"
        :class="{ disabled: !canPublish }"
        v-if="!id"
      >
        立即发布
      </button>
      <button
        class="publish-button"
        :disabled="!canPublish"
        @click="handleUpdateWork"
        :class="{ disabled: !canPublish }"
        v-else
      >
        更新作品
      </button>
      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </view>
  </view>
</template>

<script>
import { createWork, updateWork } from '@/api/hairdressing.js'

export default {
  data() {
    return {
      id: null, // 作品ID
      storeId: 0, // 商家ID
      titleFocus: false,
      tagInput: '',
      maxImages: 9,
      publishForm: {
        title: '',
        images: [],
        tags: [],
        content: '',
        publishNow: true,
      },
      popularTags: ['时尚', '减龄', '气质', '个性', '清新', '优雅', '甜美', '帅气', '潮流', '经典'],
    }
  },

  computed: {
    canPublish() {
      return (
        this.publishForm.title.trim() &&
        this.publishForm.images.length > 0 &&
        this.publishForm.content.trim()
      )
    },
  },

  onLoad(options) {
    console.log('🚀 ~ onLoad ~ options:', options)

    // 获取商家ID
    if (options.storeId) {
      this.storeId = options.storeId
    }
    if (options.info) {
      const info = JSON.parse(decodeURIComponent(options.info))
      console.log('🚀 ~ onLoad ~ info:', info)
      this.publishForm.title = info.title || ''
      this.publishForm.images = info.files || []
      this.publishForm.tags = info.tags || []
      this.publishForm.content = info.content || ''
      this.id = info.id || null
    }
  },

  methods: {
    previewImage(index) {
      uni.previewImage({
        current: index,
        urls: this.publishForm.images,
      })
    },
    // 检查是否有表单数据
    hasFormData() {
      return (
        this.publishForm.title.trim() ||
        this.publishForm.images.length > 0 ||
        this.publishForm.content.trim() ||
        this.publishForm.tags.length > 0
      )
    },

    // 选择图片
    chooseImage() {
      const remainCount = this.maxImages - this.publishForm.images.length
      uni.chooseImage({
        count: remainCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          console.log('🚀 ~ chooseImage ~ res:', res)
          // 循环选择的图片
          res.tempFilePaths.forEach((filePath) => {
            this.$util.uploadImgs(
              'upload/image',
              filePath,
              (res) => {
                console.log('🚀 ~ chooseImage ~ upload res:', res)
                this.publishForm.images.push(res.data.path)
              },
              (err) => {
                console.error('上传图片失败:', err)
              },
            )
          })
        },
      })
    },

    // 移除图片
    removeImage(index) {
      this.publishForm.images.splice(index, 1)
    },

    // 标签输入处理
    onTagInput(e) {
      this.tagInput = e.detail.value
    },

    // 添加标签
    addTag() {
      const tag = this.tagInput.trim()
      if (tag && !this.publishForm.tags.includes(tag) && this.publishForm.tags.length < 3) {
        this.publishForm.tags.push(tag)
        this.tagInput = ''
      }
    },

    // 添加热门标签
    addPopularTag(tag) {
      if (!this.publishForm.tags.includes(tag) && this.publishForm.tags.length < 3) {
        this.publishForm.tags.push(tag)
      }
    },

    // 移除标签
    removeTag(index) {
      this.publishForm.tags.splice(index, 1)
    },

    // 发布作品
    async publishWork() {
      if (!this.canPublish) {
        uni.showToast({
          title: '请完善作品信息',
          icon: 'none',
        })
        return
      }

      try {
        uni.showLoading({ title: '发布中...' })
        const params = {
          title: this.publishForm.title.trim(),
          files: this.publishForm.images.join(','), // API只支持单张图片
          tags: this.publishForm.tags.join(','),
          content: this.publishForm.content.trim(),
        }

        const res = await createWork(this.storeId, params)

        if (res.status === 200) {
          this.$util.Tips(
            { title: '保存成功' },
            {
              tab: 3,
            },
          )
        }
      } catch (error) {
        this.$util.Tips({ title: '发布失败' })
      } finally {
        uni.hideLoading()
      }
    },
    // 更新作品
    async handleUpdateWork() {
      if (!this.canPublish) {
        uni.showToast({
          title: '请完善作品信息',
          icon: 'none',
        })
        return
      }
      try {
        uni.showLoading({ title: '发布中...' })
        const params = {
          title: this.publishForm.title.trim(),
          files: this.publishForm.images.join(','), // API只支持单张图片
          tags: this.publishForm.tags.join(','),
          content: this.publishForm.content.trim(),
        }
        const res = await updateWork(6, this.storeId, params)
        if (res.status === 200) {
          uni.showToast({
            title: '更新成功',
            icon: 'success',
          })
          // 延迟返回，让用户看到成功提示
          setTimeout(() => {
            uni.navigateBack({ delta: 2 })
          }, 1500)
        }
      } catch (error) {
        this.$util.Tips({ title: '发布失败' })
      } finally {
        uni.hideLoading()
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.publish-work {
  background: #f8f8f8;
  min-height: 100vh;
}

.form-container {
  padding: 20rpx;
}

.form-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;

  .section-title {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 24rpx;

    .required {
      color: #e93323;
      margin-left: 8rpx;
    }
  }
}
.publish-button {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #d19e58, #f96e29);
  color: white;
  font-size: 32rpx;
  border-radius: 40rpx;
  text-align: center;
  line-height: 80rpx;
  transition: all 0.3s ease;

  &.disabled {
    background: #ccc;
    color: #666;
    cursor: not-allowed;
  }
}

.image-upload-area {
  .upload-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .image-item {
      position: relative;
      width: 200rpx;
      height: 200rpx;
      border-radius: 16rpx;
      overflow: hidden;

      image {
        width: 100%;
        height: 100%;
      }

      .remove-btn {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40rpx;
        height: 40rpx;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        color: white;

        .iconfont {
          font-size: 24rpx;
        }
      }
    }

    .upload-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 200rpx;
      height: 200rpx;
      border: 2rpx dashed #ddd;
      border-radius: 16rpx;
      color: #999;
      background: #fafafa;
      transition: all 0.3s ease;

      &:active {
        border-color: #e93323;
        color: #e93323;
        background: #fff5f5;
      }

      .iconfont {
        font-size: 48rpx;
        margin-bottom: 12rpx;
      }

      .upload-text {
        font-size: 28rpx;
        margin-bottom: 8rpx;
      }

      .upload-limit {
        font-size: 24rpx;
        color: #ccc;
      }
    }
  }
}

.input-wrapper,
.textarea-wrapper {
  position: relative;

  .char-count {
    position: absolute;
    bottom: 16rpx;
    right: 16rpx;
    font-size: 24rpx;
    color: #999;
  }
}

.form-input {
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  font-size: 30rpx;
  background: #fafafa;
  transition: all 0.3s ease;

  &:focus {
    border-color: #e93323;
    background: white;
  }
}

.form-textarea {
  width: 100%;
  box-sizing: border-box;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 16rpx;
  font-size: 30rpx;
  background: #fafafa;
  line-height: 1.6;
  transition: all 0.3s ease;

  &:focus {
    border-color: #e93323;
    background: white;
  }
}

.tags-container {
  .tags-input-wrapper {
    margin-bottom: 20rpx;
  }

  .tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-bottom: 24rpx;

    .tag-item {
      display: flex;
      align-items: center;
      padding: 8rpx 16rpx;
      background: linear-gradient(135deg, #d19e58, #f96e29);
      color: white;
      border-radius: 30rpx;
      font-size: 26rpx;

      .remove-tag {
        margin-left: 12rpx;
        font-size: 32rpx;
        font-weight: bold;
        cursor: pointer;
      }
    }
  }

  .popular-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16rpx;

    .popular-title {
      font-size: 26rpx;
      color: #666;
      margin-right: 8rpx;
    }

    .popular-tag {
      padding: 8rpx 16rpx;
      background: #f0f0f0;
      color: #666;
      border-radius: 30rpx;
      font-size: 24rpx;
      transition: all 0.3s ease;

      &:active {
        background: #e93323;
        color: white;
        transform: scale(0.95);
      }
    }
  }
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .setting-label {
    display: flex;
    align-items: center;
    font-size: 30rpx;
    color: #333;

    .iconfont {
      font-size: 32rpx;
      color: #e93323;
      margin-right: 12rpx;
    }
  }
}

.setting-desc {
  margin-top: 16rpx;
  font-size: 26rpx;
  color: #999;
  line-height: 1.4;
}

.safe-area-bottom {
  height: calc(2rpx+ constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
  height: calc(2rpx + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
}
</style>
