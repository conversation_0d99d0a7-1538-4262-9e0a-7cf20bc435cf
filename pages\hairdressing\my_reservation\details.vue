<template>
  <view class="refund-page" v-if="appointmentInfo.id">
    <!-- <view class="refund-status">
      <view class="">
        <text class="iconfont icon-a-ic_CompleteSelect"></text>
        <text class="status-text">已完成</text>
        <view class="status-desc">已到店</view>
      </view>
    </view> -->
    <view class="refund-status">
      <view class="">
        <!-- <text class="iconfont icon-a-ic_CompleteSelect"></text> -->
        <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/89de8202508022030489379.png" mode="widthFix" class="icon-img"></image>
        <text class="status-text">待服务</text>
        <view class="status-desc">请在约定时间内到店，超时需重新预约</view>
      </view>
    </view>
    <!-- <view class="refund-status">
      <view class="">
        <text class="iconfont icon-ic_close2"></text>
        <text class="status-text">已取消</text>
      </view>
    </view> -->
    <!-- <view class="refund-status">
      <view class="">
        <text class="iconfont icon-ic-complete1"></text>
        <text class="status-text">预约成功</text>
      </view>
    </view> -->
    <!-- 门店信息 -->
    <view class="card shop-card">
      <image :src="appointmentInfo.merchant.mer_avatar" mode="scaleToFill" class="shop-title-img" />
      <view class="shop-title-row">
        <view class="shop-title">
          {{ appointmentInfo.merchant.mer_name }}
          <text class="iconfont icon-ic_rightarrow"></text>
        </view>
        <view class="shop-address">
          {{ appointmentInfo.merchant.mer_address }}
          <text class="iconfont icon-icon_copy copy-btn" @tap="copyAddress"></text>
        </view>
      </view>
    </view>
    <!-- 订单信息卡片 -->
    <view class="card orderinfo-card">
      <view class="section-title">订单信息</view>
      <view class="orderinfo-row">
        <text class="label">预约时间</text>
        <text class="value">{{ appointmentInfo.day }} {{ appointmentInfo.timetable }}</text>
      </view>
      <view class="orderinfo-row">
        <text class="label">预约发型师</text>
        <text class="value">{{ appointmentInfo.service_data.nickname }}</text>
      </view>
      <view class="orderinfo-row">
        <text class="label">服务项目</text>
        <text class="value">{{ appointmentInfo.store_name }}</text>
      </view>
    </view>

    <view class="bottom-bar">
      <view class="item" @click="openCustomerService">
        <image src="https://ylqx-file.oss-cn-chengdu.aliyuncs.com/def/74975202508022021165931.png" mode="widthFix" class="icon"></image>
        <view class="text">客服咨询</view>
      </view>
      <button hover-class="button-hover" class="btn center" @click="cancelAppointment">
        取消预约
      </button>
    </view>
  </view>
</template>

<script>
import roleTagsVue from '@/components/roleTags/roleTags.vue'
import zbCode from '@/components/zb-code/zb-code.vue'
import { getAppointmentList } from '@/api/hairdressing.js'

export default {
  components: { roleTagsVue, zbCode },
  data() {
    return {
      codeShow: true,
      cid: '',
      val: '',
      size: 200,
      unit: 'rpx',
      background: '#ffffff',
      foreground: '#000000',
      pdground: '#ffffff',
      icon: '',
      iconsize: 40,
      onval: '',
      loadMake: false,
      //预约信息
      appointmentInfo: {},
    }
  },
  onLoad(options) {
    // 获取传递的参数
    if (options.info) {
      this.appointmentInfo = JSON.parse(options.info)
    }
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
    copyAddress() {
      uni.setClipboardData({ data: this.appointmentInfo.merchant.mer_address })
    },
    reorder() {
      // 跳转到下单页面
    },
    cancelAppointment() {
      uni.showModal({
        title: '确认取消',
        content: '确定要取消这个预约吗？',
        success: (res) => {
          if (res.confirm) {
            // 调用取消预约API
            deleteAppointment(this.appointmentInfo.id).then(() => {
              this.$util.Tips(
                { title: '取消成功' },
                {
                  tab: 3,
                },
              )
            })
          }
        },
      })
    },
    openCustomerService() {
      this.$util.jumpKefu()
    },
  },
}
</script>

<style scoped lang="scss">
.refund-page {
  padding: 30rpx 30rpx 120rpx;
}
.card {
  padding: 40rpx 20rpx;
  background-color: white;
  border-radius: 30rpx;
}
.status-text {
  font-size: 32rpx;
  color: #d0ac6e;
  font-weight: bold;
}

.shop-card {
  margin-top: 10rpx;
  display: flex;
  align-items: center;
  .iconfont {
    font-size: 28rpx;
    color: #999999;
    margin-left: 10rpx;
  }
  .shop-title-img {
    width: 98rpx;
    height: 98rpx;
    border-radius: 12rpx;
    margin-right: 20rpx;
  }
  .shop-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #222;
  }
  .shop-address {
    color: #333;
    font-size: 20rpx;
    font-size: 400;
    margin-top: 12rpx;
    display: flex;
    align-items: center;
  }
  .copy-btn {
    font-size: 28rpx;
    color: #999;
    margin-left: 10rpx;
  }

  .distance {
    color: #d0ac6e;
    margin-left: 8rpx;
    font-size: 26rpx;
  }
}

.orderinfo-card {
  margin-top: 10rpx;
}

.row-code {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 26rpx;
  border-top: 1rpx dashed #e6e6e6;
  padding-top: 48rpx;
  color: #333333;
  .status {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    margin-left: 20rpx;
  }
  .btn {
    width: fit-content;
    font-weight: 400;
    font-size: 20rpx;
    color: #999999;
    border-radius: 30rpx;
    border: 1px solid #999999;
    padding: 10rpx 20rpx;
    margin-left: auto;
  }
}

.orderinfo-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
  .label {
    color: #333;
    font-weight: 400;
    font-size: 28rpx;
  }
  .value {
    color: #666;
    font-size: 28rpx;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10rpx 24rpx;
  background: white;
  z-index: 20;
}

.btn-primary {
  width: 100%;
  border-radius: 40rpx;
  background: #c9a063;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  padding: 24rpx 0;
  border: none;
}
.refund-status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 50rpx 0;
  text-align: center;

  .iconfont {
    font-size: 38rpx;
    color: #d19e58;
    margin-right: 12rpx;
  }
  .status-desc {
    font-weight: 400;
    font-size: 22rpx;
    color: #333333;
    margin-top: 30rpx;
  }
  .icon-img {
    width: 48rpx;
    height: 46rpx;
  }
}
.section-title {
  font-size: 28rpx;
  color: #333333;
  font-weight: bold;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 24rpx;
  display: flex;
  align-items: center;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 20rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 20rpx);
  border-top: 1rpx solid #f0f0f0;
  .item {
    text-align: center;
    color: #666666;
    margin-right: 50rpx;
    .icon {
      width: 42rpx;
      height: 42rpx;
    }
    .text {
      font-weight: 400;
      font-size: 20rpx;
      color: #999999;
      margin-top: 6rpx;
    }
  }
  .btn {
    // width: 428rpx;
    flex: 1;
    height: 78rpx;
    background: #c9a063;
    border-radius: 39rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #ffffff;
  }
}
</style>
