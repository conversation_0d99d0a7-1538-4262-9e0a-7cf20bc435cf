{
    "name" : "御理清享",
    "appid" : "__UNI__A91C157",
    "description" : "",
    "versionName" : "1.0.4",
    "versionCode" : 127,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "titleNView" : true,
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "VideoPlayer" : {},
            "OAuth" : {},
            "Payment" : {},
            "Share" : {},
            "iBeacon" : {},
            "Geolocation" : {},
            "Maps" : {}
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_LOCATION_EXTRA_COMMANDS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.BATTERY_STATS\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_CONFIGURATION\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH\"/>",
                    "<uses-permission android:name=\"android.permission.BLUETOOTH_ADMIN\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ],
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "permissionPhoneState" : {
                    "request" : "none" //拨打电话权限关闭
                }
            },
            /* ios打包配置 */
            "ios" : {
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "上传用户头像保存分享海报",
                    "NSPhotoLibraryAddUsageDescription" : "上传用户头像保存分享海报",
                    "NSCameraUsageDescription" : "上传用户头像保存分享海报",
                    "NSLocationWhenInUseUsageDescription" : "根据客户地理位置推荐最近门店",
                    "NSLocationAlwaysUsageDescription" : "根据客户地理位置推荐最近门店"
                },
                "permissions" : {
                    "NSAppTransportSecurity" : {
                        "NSAllowsArbitraryLoads" : true
                    }
                },
                "idfa" : false
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "",
                        "appkey_android" : ""
                    }
                },
                "payment" : {
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                },
                "share" : {
                    "weixin" : {
                        "appid" : "",
                        "UniversalLinks" : "https://mer.crmeb.net/"
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "",
                        "appkey_android" : ""
                    }
                },
                "push" : {},
                "oauth" : {
                    "apple" : {},
                    "weixin" : {
                        "appid" : "",
                        "appsecret" : "",
                        "UniversalLinks" : "https://mer.crmeb.net/"
                    }
                },
                "ad" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "useOriginalMsgbox" : true
            }
        },
        "nvueLaunchMode" : ""
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wxf7d90a13ca49df99",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : true,
            "es6" : true
        },
        "permission" : {
            "scope.userLocation" : {
                "desc" : "获取您的位置"
            }
        },
        "optimization" : {
            "subPackages" : true
        },
        "requiredPrivateInfos" : [ "getLocation", "chooseAddress" ],
        "usingComponents" : true,
        "__usePrivacyCheck__" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "devServer" : {
            "https" : false,
            "proxy" : {
                "/api" : {
                    "target" : "http://test.mall.yuliqingxiang.cn",
                    "changeOrigin" : true,
                    "secure" : false,
                    "pathRewrite" : {
                        "^/api" : ""
                    }
                }
            }
        },
        "router" : {
            "mode" : "history",
            "base" : ""
        },
        "domain" : "",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "SMJBZ-WCHK4-ZPZUA-DSIXI-XDDVQ-XWFX7"
                }
            }
        },
        "title" : "加载中...",
        "template" : "template.h5.html"
    }
}
